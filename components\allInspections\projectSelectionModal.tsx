// 'use client';
// import React, { useEffect, useState } from "react";
// import { message, Modal, Radio, Spin } from "antd";
// import { useTranslation } from "react-i18next";
// import { Poppins } from 'next/font/google';
// import { fetchAllDocuments } from "@/src/services/project.api";

// const poppins = Poppins({ subsets: ['latin'], weight: ['400', '700'] });

// interface ProjectSelectionModalProps {
//   visible: boolean;
//   onCancel: () => void;
//   onSelectProject: (project: string) => void;
//   // projectNames: any;
// }

// const ProjectSelectionModal: React.FC<ProjectSelectionModalProps> = ({
//   visible,
//   onCancel,
//   onSelectProject,
//   // projectNames,
// }) => {
//   const { t } = useTranslation();
//   const [loading, setLoading] = useState<boolean>(false);
//   const [selectedProject, setSelectedProject] = useState<any | null>(null);
//   const  [documents, setDocuments] = useState<any[]>([]); // Adjust the type as per your data structure

//   useEffect(() => {
//   if (!visible) return; // Only run when modal is shown

//   const fetchDocuments = async () => {
//   const userId: any = localStorage.getItem("ScalarUserId");
//   console.log("Fetching documents...");
//   setLoading(true);

//   try {
//     const res = await fetchAllDocuments(userId);
//     if (res) {
//       console.log("Fetched documents:", res);
//       setDocuments(res);
//     } else {
//       message.error("Failed to fetch documents");
//     }
//   } catch (error) {
//     message.error("An error occurred while fetching documents");
//   } finally {
//     setLoading(false);
//   }
// };

//   fetchDocuments();
// }, [visible]);

//   const handleOk = () => {
//     if (selectedProject) {
//       onSelectProject(selectedProject); // Pass the project name as string
//       setSelectedProject(null); // Optional: reset selection
//     }
//   };

//   const handleCancel = () => {
//     onCancel();
//     setSelectedProject(null); // Optional: reset selection
//   };

//   return (
//     <Modal
//       title={t("Select a Project")}
//       open={visible}
//       centered
//       width={350}
//       footer={
//         <div className="w-full flex justify-center gap-4 mt-8">
//           <button
//             onClick={handleCancel}
//             className={`${poppins.className} px-4 rounded-xl text-[15px] border border-[#C4C4C4] bg-white text-[#333] h-[40px] w-[30%] hover:bg-gray-100 transition`}
//           >
//             {t("Cancel")}
//           </button>
//           <button
//             onClick={handleOk}
//             disabled={!selectedProject}
//             className={`${poppins.className} px-4 rounded-xl text-[15px] bg-[#2F80ED] text-white h-[40px] w-[30%] disabled:opacity-50 hover:bg-[#2F80ED] transition`}
//           >
//             {t("Save")}
//           </button>
//         </div>
//       }
//       onCancel={handleCancel}
//       bodyStyle={{ maxHeight: "300px", overflowY: "auto", paddingRight: "6px" }}
//     >
//       <Spin spinning={loading}>
//       <Radio.Group
//         className="mt-4 text-[16px] font-[400] text-[#333333]"
//         onChange={(e) => setSelectedProject(e.target.value)}
//         value={selectedProject}
//         style={{ display: "flex", flexDirection: "column", gap: "14px" }}
//       >
//         {documents.map((project: any) => (
//           <Radio key={project.id} value={project} className="!flex items-center gap-2">
//             {project.name}
//           </Radio>
//         ))}
//       </Radio.Group>
//       </Spin>
//     </Modal>
//   );
// };

// export default ProjectSelectionModal;

"use client";
import React, { useEffect, useState } from "react";
import { message, Modal, Radio, Spin } from "antd";
import { useTranslation } from "react-i18next";
import { Poppins } from "next/font/google";
import { fetchAllDocuments } from "@/src/services/project.api";
import InfiniteScroll from "react-infinite-scroll-component";
import { LoadingOutlined } from "@ant-design/icons";

const poppins = Poppins({ subsets: ["latin"], weight: ["400", "700"] });

interface ProjectSelectionModalProps {
  visible: boolean;
  onCancel: () => void;
  onSelectProject: (project: string) => void;
}

const PAGE_SIZE = 10; // Number of items to load at once

const ProjectSelectionModal: React.FC<ProjectSelectionModalProps> = ({
  visible,
  onCancel,
  onSelectProject,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<any | null>(null);
  const [allDocuments, setAllDocuments] = useState<any[]>([]);
  const [visibleDocuments, setVisibleDocuments] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState<boolean>(true);

  useEffect(() => {
    if (!visible) return;

    const fetchDocuments = async () => {
      const userId: any = localStorage.getItem("ScalarUserId");
      setLoading(true);
      try {
        const res = await fetchAllDocuments(userId);
        if (res) {
          setAllDocuments(res);
          setVisibleDocuments(res.slice(0, PAGE_SIZE));
          setHasMore(res.length > PAGE_SIZE);
        } else {
          message.error("Failed to fetch documents");
        }
      } catch (error) {
        message.error("An error occurred while fetching documents");
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [visible]);

  const loadMoreData = () => {
    const currentLength = visibleDocuments.length;
    const nextLength = currentLength + PAGE_SIZE;
    const nextSlice = allDocuments.slice(0, nextLength);

    setVisibleDocuments(nextSlice);

    if (nextLength >= allDocuments.length) {
      setHasMore(false);
    }
  };

  const handleOk = () => {
    if (selectedProject) {
      onSelectProject(selectedProject);
      setSelectedProject(null);
    }
  };

  const handleCancel = () => {
    onCancel();
    setSelectedProject(null);
  };

  console.log("selectedProject", selectedProject);

  return (
    <Modal
      title={t("Select a Project")}
      open={visible}
      centered
      width={350}
      footer={
        <div className="w-full flex justify-center gap-4 mt-8">
          <button
            onClick={handleCancel}
            className={`${poppins.className} px-4 rounded-xl text-[15px] border border-[#C4C4C4] bg-white text-[#333] h-[40px] w-[30%] hover:bg-gray-100 transition`}
          >
            {t("Cancel")}
          </button>
          <button
            onClick={handleOk}
            disabled={!selectedProject}
            className={`${poppins.className} px-4 rounded-xl text-[15px] bg-[#2F80ED] text-white h-[40px] w-[30%] disabled:opacity-50 hover:bg-[#2F80ED] transition`}
          >
            {t("Save")}
          </button>
        </div>
      }
      onCancel={handleCancel}
      // bodyStyle={{ maxHeight: "300px", overflowY: "auto", paddingRight: "6px" }}
    >
      {" "}
      {/* <Spin spinning={loading}> */}
      <div
        id="scrollableDiv"
        style={{ maxHeight: 300, overflow: "auto", paddingRight: 6 }}
      >
        <InfiniteScroll
          dataLength={visibleDocuments.length}
          next={loadMoreData}
          hasMore={hasMore}
          loader={
            <div className="flex justify-center items-center py-8 ">
              {/* <Spin /> */}
              <Spin size="large" indicator={<LoadingOutlined spin />} />
            </div>
          }
          // endMessage={
          //   <div className="text-center text-gray-400 py-2">
          //     {t("No more projects")}
          //   </div>
          // }
          scrollableTarget="scrollableDiv"
        >
          <Radio.Group
            onChange={(e) => setSelectedProject(e.target.value)}
            value={selectedProject}
            style={{ display: "flex", flexDirection: "column", gap: "14px" }}
          >
            {visibleDocuments.map((project: any) => (
              <Radio
                key={project.id}
                value={project}
                className="!flex items-center gap-2"
              >
                {project.name}
              </Radio>
            ))}
          </Radio.Group>
        </InfiniteScroll>
      </div>
      {/* </Spin> */}
    </Modal>
  );
};

export default ProjectSelectionModal;
