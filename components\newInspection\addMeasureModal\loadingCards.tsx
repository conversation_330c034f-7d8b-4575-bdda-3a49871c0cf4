import React from "react";
import css from "@/components/newInspection/common/newFindings.module.css";

const LoadingCards = () => {
  return (
    <>
      <div
          className="bg-white w-full box-border rounded-[20px] grid grid-cols-6 gap-8 py-4"
        >
          <h1
            className={`flex justify-center col-span-1 text-[16px] leading-[16px] font-[500] text-center`}
          >
            <p
              className={`w-full h-[40vh] bg-[#f5f5f5] rounded-lg ${css.shimmer}`}
            ></p>
          </h1>
          <h1
            className={`w-full flex flex-col gap-8 justify-center col-span-5 text-[16px] leading-[16px] font-[500] text-center`}
          >
            <p
              className={`w-full h-[10vh] bg-[#f5f5f5] rounded-lg ${css.shimmer}`}
            ></p>
            <p
              className={`w-full h-[10vh] bg-[#f5f5f5] rounded-lg ${css.shimmer}`}
            ></p>
            <p
              className={`w-full h-[10vh] bg-[#f5f5f5] rounded-lg ${css.shimmer}`}
            ></p>
          </h1>
        </div>
    </>
  );
};

export default LoadingCards;
