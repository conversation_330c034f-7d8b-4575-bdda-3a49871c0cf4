{"name": "scalar-agency-web", "version": "0.1.0", "private": true, "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^5.16.0", "@mui/material": "^5.16.0", "@react-google-maps/api": "^2.20.3", "@sentry/nextjs": "^8.34.0", "@sentry/replay": "^7.116.0", "@sentry/tracing": "^7.114.0", "antd": "^5.19.1", "axios": "^1.7.2", "browser-image-compression": "^2.0.2", "dayjs": "^1.11.13", "debounce": "^2.2.0", "firebase": "^10.12.3", "framer-motion": "^11.3.31", "i18next": "^23.12.2", "moment": "^2.30.1", "mui": "^0.0.1", "next": "14.2.4", "react": "^18", "react-dom": "^18", "react-google-autocomplete": "^2.7.4", "react-i18next": "^15.0.0", "react-infinite-scroll-component": "^6.1.0", "uuid": "^11.0.3", "yarn": "^1.22.22"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}