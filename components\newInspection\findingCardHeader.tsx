import Image from "next/image";
import React from "react";
import { generateFindingTitle, handleMoreFileDrop } from "./utils";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

const FindingCardHeader = ({
    finding,
    provided,
    snapshot,
    groupedFindings,
    setWholeGroup,
    setIsEditFindingModalOpen,
    setIsApproveFindingModalOpen,
    loadingIds,
    group,
    setLoadingIds,
    fetchFindings
}: any) => {
  return (
    <div
      ref={provided.innerRef}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
      className={`p-3 mr-1 mb-2 rounded-md border ${
        snapshot.isDragging ? "shadow-lg bg-blue-100" : "shadow-sm bg-blue-50"
      } ${
        finding?.approved ? "text-[#BFBFBF]" : "text-blue-600 animate-pulse"
      } hover:!cursor-pointer`}
      onDrop={(e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!finding?.approved) {
          handleMoreFileDrop(e, finding.id, finding?.media, setLoadingIds, fetchFindings);
        }
      }}
      onDragOver={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      onClick={(e) => {
        e.stopPropagation();
        const currentGroup = groupedFindings.find(
          (g: any) => g.id === group.id
        );
        if (currentGroup) {
          const additionalFindings = group.findings.filter(
            (finding: any) =>
              !currentGroup.decompositionItem.some(
                (item: any) => item.id === finding.id
              )
          );
          currentGroup.decompositionItem = [
            ...currentGroup.decompositionItem,
            ...additionalFindings,
          ];
          setWholeGroup(currentGroup);
          const resizeMedia = currentGroup.parent.media.map((item: any) => {
            return {
              isVideo: item.isVideo,
              url: item.resize_url,
            };
          });
          localStorage.setItem(
            "decompositionItemMedia",
            JSON.stringify(resizeMedia)
          );
        } else {
          setWholeGroup({
            id: group.id,
            parent: group.decomposition,
            title_fields: group.decomposition.title_fields,
            decompositionItem: [group.decomposition, ...group.findings],
          });
          const resizeMedia = group.decomposition.media.map((item: any) => {
            return {
              isVideo: item.isVideo,
              url: item.resize_url,
            };
          });
          localStorage.setItem(
            "decompositionItemMedia",
            JSON.stringify(resizeMedia)
          );
        }
        // return
        sessionStorage.setItem("currentId", finding.id);
        const id: any = process.env.NEXT_FINDING_ID;
        sessionStorage.setItem("FindingTypeId", id);
        localStorage.setItem("isFindingUpdate", "true");
        localStorage.setItem("isMeasureUpdate", "false");
        localStorage.setItem("findingId", finding.id);
        localStorage.setItem("parentId", group.id);
        localStorage.setItem(
          "updateFindingObj",
          JSON.stringify(finding.field_values)
        );

        // return
        if (finding.approved) {
          setIsEditFindingModalOpen(true);
        } else {
          setIsApproveFindingModalOpen(true);
        }
      }}
    >
      <div className="flex justify-start gap-2">
        {!finding?.approved && loadingIds.includes(finding.id) && (
          <div className="text-[12px] items-cente">
            <Spin indicator={<LoadingOutlined spin />} size="small" />
          </div>
        )}
        <div
          className={`${
            finding?.approved ? "w-[calc(100%-30px)]" : ""
          } flex items-start gap-2 break-words text-[12px]`}
        >
          <h1 className="">
            {finding.title_fields && finding.title_fields[0]
              ? generateFindingTitle(
                  finding.title_fields,
                  finding.fields,
                  0,
                  group.decomposition
                )
              : "Finding"}
          </h1>
        </div>
        {finding?.approved && (
          <Image
            src={"/images/newInspection/yes.png"}
            width={20}
            height={20}
            alt=""
            className="w-[20px] h-[20px] mt-[2px]"
          />
        )}
      </div>
    </div>
  );
};

export default FindingCardHeader;
