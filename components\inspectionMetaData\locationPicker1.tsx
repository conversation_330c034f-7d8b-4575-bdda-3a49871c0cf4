// LocationPicker.tsx
"use client"
import React, { useState, useEffect, useCallback } from 'react';
import { GoogleMap, Libraries, Marker, useLoadScript } from '@react-google-maps/api';
import { useTranslation } from "react-i18next";
import { Input } from 'antd';

const mapContainerStyle = {
  width: '100%',
  height: '100%',
  borderRadius: "10px",
};

const libraries: Libraries = ['places'];

interface LocationPickerProps {
  googleMapsApiKey: string;
  location: { lat: number; lon: number } | null;
  setLocation: (lat: number, lon: number) => void;
  isLoaded: any;
  loadError: any;
}

const blueDot = "/images/home/<USER>";
const redMarker = "/images/home/<USER>";


const LocationPicker: React.FC<LocationPickerProps> = ({
  location,
  setLocation,
  isLoaded,
  loadError
}) => {

  const { t } = useTranslation();

  const [currentLocation, setCurrentLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [markerPosition, setMarkerPosition] = useState<{ lat: number; lng: number } | null>(null);
  const [address, setAddress] = useState<string>('');
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [isSelectingFromSuggestion, setIsSelectingFromSuggestion] = useState(false); // Flag to track suggestion selection

  useEffect(() => {
    if (location) {
      setMarkerPosition({ lat: location.lat, lng: location.lon });
      reverseGeocode(location.lat, location.lon);
    }

    // Fetch the user's current location
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const userLocation = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        setCurrentLocation(userLocation);
        if (!location) {
          setMarkerPosition(userLocation);
        }
      },
      () => {
        // const fallbackLocation = { lat: 40.7128, lng: -74.006 }; // Example fallback: NYC
        // setCurrentLocation(fallbackLocation);
        // setMarkerPosition(fallbackLocation);
      }
    );
  }, [location]);

  const reverseGeocode = (lat: number, lng: number) => {
    const geocoder = new google.maps.Geocoder();
    geocoder.geocode(
      { location: { lat, lng } },
      (results, status) => {
        if (status === "OK" && results && results.length > 0) {
          const addressComponents = results[0].address_components;
          const getAddressComponent = (type: any) =>
            addressComponents.find((component: any) =>
              component.types.includes(type)
            )?.long_name || "";

          // console.log('results', results[0])
          // console.log('addressComponents', addressComponents)
          const city = getAddressComponent("locality") || "";
          const streetNumber = getAddressComponent("street_number");
          const street = getAddressComponent("route");
          // console.log('place', results[0])

          // Only update the address if it was not triggered by a suggestion selection
          if (!isSelectingFromSuggestion) {
            // console.log('but changed by marker')
            // setAddress(${streetNumber ? streetNumber + ", " : ""}${street ? street + ", " : ""}${city});
            setAddress(results[0].formatted_address);
          }
        } else {
          console.error("Geocoder failed due to: ", status);
        }
      }
    );
  };

  const handleMarkerDragEnd = useCallback((e: google.maps.MapMouseEvent) => {
    const lat = e.latLng?.lat();
    const lon = e.latLng?.lng();
    if (lat !== undefined && lon !== undefined) {
      setMarkerPosition({ lat, lng: lon });
      setLocation(lat, lon);
      reverseGeocode(lat, lon);
    }
  }, [setLocation, isSelectingFromSuggestion]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAddress(value);

    if (value.trim()) {
      const service = new google.maps.places.AutocompleteService();
      service.getPlacePredictions(
        { input: value, types: ['address'] },
        (predictions, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            setSuggestions(predictions);
          } else {
            setSuggestions([]);
          }
        }
      );
    } else {
      setSuggestions([]);
    }
  };

  const handleSuggestionSelect = (placeId: string, object: any) => {
    const service = new google.maps.places.PlacesService(document.createElement('div'));
    service.getDetails({ placeId }, (place: any, status) => {
      if (status === google.maps.places.PlacesServiceStatus.OK && place) {
        const { geometry, address_components }: any = place;
        if (geometry) {
          const { lat, lng }: any = geometry.location;

          const getAddressComponent = (type: string) =>
            address_components.find((component: any) =>
              component.types.includes(type)
            )?.long_name || "";

          // console.log('object', object)
          // console.log('address_components', address_components)

          const streetNumber = getAddressComponent("street_number");
          const street = getAddressComponent("route");
          const city = getAddressComponent("locality");

          // Update the input field directly here for suggestion selection
          // console.log('Changed inside selection', ${streetNumber ? streetNumber + ", " : ""}${street ? street + ", " : ""}${city})
          // setAddress(${streetNumber ? streetNumber + ", " : ""}${street ? street + ", " : ""}${city});
          setAddress(place.formatted_address)
          setIsSelectingFromSuggestion(true); // Set flag to prevent reverse geocode from updating address
          setMarkerPosition({ lat: lat(), lng: lng() });
          setLocation(lat(), lng());
          setSuggestions([]);

          // Reset the flag after the marker position has been updated
          setTimeout(() => {
            setIsSelectingFromSuggestion(false);
          }, 100); // Add a small delay to ensure the flag resets correctly
        }
      }
    });
  };

  if (loadError) return <div>{t("Something went wrong, try again later!")}</div>;
  if (!isLoaded) return <div>{t("Loading Map")}...</div>;

  return (
    <div className='w-full h-full flex flex-col gap-2'>
      {/* Input Field for Search */}
      <div className="relative">
        <Input
          type="text"
          value={address}
          onChange={handleInputChange}
          allowClear
          onPressEnter={() => {
            if (suggestions.length > 0) {
              handleSuggestionSelect(suggestions[0]?.place_id, suggestions[0])
            }
          }}
          placeholder={t("Enter a location")}
          className="w-full h-[40px] p-2 px-3 border rounded-lg bg-white text-black focus:outline-none focus-visible:outline-none"
        />
        {/* Suggestions Dropdown */}
        {suggestions.length > 0 && (
          <ul className="absolute z-10 bg-white border border-gray-300 w-full rounded-lg mt-1 max-h-[40vh] overflow-auto">
            {suggestions.map((suggestion) => (
              <li
                key={suggestion.place_id}
                onClick={() => handleSuggestionSelect(suggestion.place_id, suggestion)}
                className="p-2 cursor-pointer hover:bg-gray-200"
              >
                {suggestion.description}
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Map */}
      <div className='w-full h-[calc(100%-50px)]'>
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          zoom={10}
          center={markerPosition || { lat: 0, lng: 0 }}
          options={{
            streetViewControl: false,
            fullscreenControl: false,
          }}
        >
          {/* Blue dot for current location */}
          {currentLocation && (
            <Marker
              position={currentLocation}
              icon={{
                url: blueDot,
              }}
            />
          )}

          {/* Draggable red pin */}
          {markerPosition && (
            <Marker
              position={markerPosition}
              draggable
              onDragEnd={handleMarkerDragEnd}
              icon={{
                url: redMarker,
              }}
            />
          )}
        </GoogleMap>
      </div>
    </div>
  );
};

export default LocationPicker;