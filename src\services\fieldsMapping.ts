import { fetchInspection } from "./inspectionDetails.api"

export const mapFiledsWithValue = (fields: any, valueFields: any) => {

    const parsedFields = typeof fields === "object" ? fields : JSON.parse(fields)
    const parsedValueFields = typeof valueFields === "object" ? valueFields : JSON.parse(valueFields)

    const mappedfields = parsedFields?.map((field: any) => {
        const f = parsedValueFields?.find((f: any) => f.id === field.id)
        if (f) {
            return {
                ...field,
                suggested_value: f?.suggested_value ? f.suggested_value : null
            }
        } else {
            return field
        }
    })

    return mappedfields;
}

export const fetchInspectionAndMapFields = async (valueFields: any, type: any) => {
    const inspectionId: any = localStorage.getItem("ScalarInspectionId");

    const inspectionDetails: any = await fetchInspection(inspectionId);

    let suggestionFields: any;

    if (type === process.env.NEXT_DECOMP_ID) {
        suggestionFields = JSON.parse(
            inspectionDetails?.decomp_fields
        )?.fields;
    } else if (type === process.env.NEXT_FINDING_ID) {
        suggestionFields = JSON.parse(
            inspectionDetails?.finding_fields
        )?.fields;
    } else {
        suggestionFields = JSON.parse(
            inspectionDetails?.measure_fields
        )?.fields;
    }

    const mappedFields = mapFiledsWithValue(suggestionFields, valueFields);

    return mappedFields
}

export const createDBFieldObj = (fields: any) => {
    return JSON.stringify(fields.map((field: any) => ({ id: field?.id, suggested_value: field?.suggested_value })))
}