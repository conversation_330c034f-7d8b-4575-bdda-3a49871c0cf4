import React, { useState } from "react";
import { Button, message } from "antd";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { Poppins } from "next/font/google";
import ExportModal from "./exportModal";
import MarkCompleteModal from "./markCompleteModal";
import MarkTodoModal from "./markTodoModal";
import DeleteModal from "./deleteModal";

const poppins = Poppins({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const ActionButtons = ({
  setSearchedInspections,
  searchedInspections,
  setRefresh,
  setIsActionMenuVisible,
  selectedInspections,
  t,
}: any) => {
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isMarkCompleteModalOpen, setIsMarkCompleteModalOpen] = useState(false);
  const [isMarkTodoModalOpen, setIsMarkTodoModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const exportInspections = async () => {
    if (selectedInspections.length === 0) {
      message.info(t("Select inspections to export."));
      return;
    }
    setIsExportModalOpen(true);
  };

  const markCompleteInspections = async () => {
    if (selectedInspections.length === 0) {
      message.info(t("Select inspections to mark as complete."));
      return;
    }
    setIsMarkCompleteModalOpen(true);
  };

  const markTodoInspections = async () => {
    if (selectedInspections.length === 0) {
      message.info(t("Select inspections to mark as todo."));
      return;
    }
    setIsMarkTodoModalOpen(true);
  };

  const deleteInspections = async () => {
    if (selectedInspections.length === 0) {
      message.info(t("Select inspections to delete."));
      return;
    }
    setIsDeleteModalOpen(true);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="p-2 absolute top-[110%] right-0 z-50 bg-white rounded-2xl flex flex-col gap-2 shadow-md border"
    >
      <Button
        onClick={exportInspections}
        type="primary"
        className={`${poppins.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white`}
      >
        {t("Export")}
      </Button>
      {/* {isExportModalOpen && ( */}
      <ExportModal
        isModalOpen={isExportModalOpen}
        setIsModalOpen={setIsExportModalOpen}
        selectedInspections={selectedInspections}
        setSearchedInspections={setSearchedInspections}
        searchedInspections={searchedInspections}
        setRefresh={setRefresh}
        setMenu={setIsActionMenuVisible}
      />
      {/* )} */}
      <Button
        onClick={markCompleteInspections}
        type="primary"
        className={`${poppins.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white `}
      >
        {t("Mark as complete")}
      </Button>
      <MarkCompleteModal
        isModalOpen={isMarkCompleteModalOpen}
        setIsModalOpen={setIsMarkCompleteModalOpen}
        selectedInspections={selectedInspections}
        setSearchedInspections={setSearchedInspections}
        searchedInspections={searchedInspections}
        setRefresh={setRefresh}
        setMenu={setIsActionMenuVisible}
      />
      <Button
        onClick={markTodoInspections}
        type="primary"
        className={`${poppins.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white `}
      >
        {t("Mark as todo")}
      </Button>
      <MarkTodoModal
        isModalOpen={isMarkTodoModalOpen}
        setIsModalOpen={setIsMarkTodoModalOpen}
        selectedInspections={selectedInspections}
        setSearchedInspections={setSearchedInspections}
        searchedInspections={searchedInspections}
        setRefresh={setRefresh}
        setMenu={setIsActionMenuVisible}
      />
      <Button
        onClick={deleteInspections}
        style={{
          backgroundColor: "#FF5656",
          color: "white",
          borderColor: "transparent",
        }}
        className={`${poppins.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] border-opacity-30 bg-[#FF5656] text-white `}
      >
        {t("Delete")}
      </Button>
      <DeleteModal
        isModalOpen={isDeleteModalOpen}
        setIsModalOpen={setIsDeleteModalOpen}
        selectedInspections={selectedInspections}
        setRefresh={setRefresh}
        setMenu={setIsActionMenuVisible}
      />
      <Button
        onClick={() => {
          setIsActionMenuVisible(false);
        }}
        type="primary"
        style={{
          backgroundColor: "#ff91000a",
          color: "#FF9200",
        }}
        className={`${poppins.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] text-[#FF9200] border border-[#FF9200] bg-[#ff91000a] hover:bg-[#ff910015]`}
      >
        {t("Cancel")}
      </Button>
    </motion.div>
  );
};

export default ActionButtons;
