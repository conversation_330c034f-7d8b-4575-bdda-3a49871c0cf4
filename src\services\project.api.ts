import axios from "axios";
import { db } from "@/firebase.config";
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  addDoc,
  deleteDoc,
  writeBatch,
  getDoc,
  orderBy,
  limit,
  startAfter,
} from "firebase/firestore";
import { fetch } from "../libs/helpers";

const host = process.env.NEXT_HOST;

export const fetchUser = async (userId: any) => {
  // const collectionRef = collection(db, "user");

  // const condition = query(
  //   collectionRef,
  //   where("firebase_uid", "==", userId)
  // );

  const docRef = doc(db, "user", userId);

  const docSnap = await getDoc(docRef);

  // const querySnapshot = await getDocs(condition);

  if (docSnap.exists()) {
    const docData = docSnap.data();
    return docData;
  } else {
    return null;
  }
};

export const getAllTeamMembers = async (clientId: any) => {
  try {
    // const clientRef = doc(db, "client", clientId);
    const collectionRef = collection(db, "user");

    const condition = query(collectionRef, where("client", "==", clientId));

    const querySnapshot = await getDocs(condition);

    const documents = querySnapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data(),
    }));
    await documents.sort((a, b) => b.created_at.seconds - a.created_at.seconds);
    return documents;
  } catch (error) {
    return false;
  }
};

export const addProjectDetail = async (
  projectDetails: any,
  inspectionType: any,
  userId: string,
  teamMembers: string[]
) => {
  const reporttypeRef = doc(db, "report_type", inspectionType);

  const users = teamMembers;

  if (!users.includes(userId)) {
    users.push(userId);
  }

  // const userRefs = await Promise.all(teamMembers.map(memberId => doc(db, "user", memberId)));

  // const currentUserRef = doc(db, "user", userId);

  // const userRefPaths = userRefs.map(ref => ref.path);

  // if (!userRefPaths.includes(currentUserRef.path)) {
  //   userRefs.push(currentUserRef);
  // }

  projectDetails.inspection_type = reporttypeRef;
  projectDetails.users = users;

  const docRef = await addDoc(collection(db, "projects"), projectDetails);
  return docRef;
};

export const fetchAllDocuments = async (userId: any) => {
  try {
    // const UserRef = doc(db, "user", userId);
    const collectionRef = collection(db, "projects");
    const clientId = localStorage.getItem("client");

    // Use `array-contains` to find documents where `users` array includes `UserRef`
    const condition = query(
      collectionRef,
      where("users", "array-contains", userId),
      where("client", "==", clientId)
    );

    const querySnapshot = await getDocs(condition);

    // Map and sort the documents by `created_at` field
    const documents = querySnapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data(),
    }));

    documents.sort((a, b) => b.created_at.seconds - a.created_at.seconds);

    return documents;
  } catch (error) {
    return false;
  }
};

export const getProjects = async ({
  search = "",
  userId = "",
  isForFirst = true,
  lastDocument = null,
  limitValue = 10,
}: any) => {
  const clientId = localStorage.getItem("client");
  let projectList: any = [];
  let hasMore = false;

  // Base query
  let queryRef;

  if (search) {
    // Query for search functionality with pagination
    queryRef = query(
      collection(db, "projects"), // Use "inspections" collection for search
      where("users", "array-contains", userId),
      where("client", "==", clientId),
      where("name", ">=", search),
      where("name", "<=", `${search}\uf8ff`),
      orderBy("name"), // Use orderBy("name") because of range query
      orderBy("created_at", "desc"),
      limit(limitValue)
    );

    if (!isForFirst && lastDocument) {
      queryRef = query(
        collection(db, "projects"),
        where("users", "array-contains", userId),
        where("client", "==", clientId),
        where("name", ">=", search),
        where("name", "<=", `${search}\uf8ff`),
        orderBy("name"),
        orderBy("created_at", "desc"),
        startAfter(lastDocument),
        limit(limitValue)
      );
    }
  } else {
    // Query for standard project fetching with pagination
    queryRef = query(
      collection(db, "projects"), // Use "projects" collection for user projects
      where("users", "array-contains", userId),
      where("client", "==", clientId),
      orderBy("created_at", "desc"),
      limit(limitValue)
    );

    if (!isForFirst && lastDocument) {
      queryRef = query(
        collection(db, "projects"),
        where("users", "array-contains", userId),
        where("client", "==", clientId),
        orderBy("created_at", "desc"),
        startAfter(lastDocument),
        limit(limitValue)
      );
    }
  }

  // Fetch data
  const querySnapshot = await getDocs(queryRef);

  // Check if there are more documents
  hasMore = querySnapshot.docs.length === limitValue;

  // Map through documents
  if (!querySnapshot.empty) {
    projectList = querySnapshot.docs.map((doc) => {
      return { ...doc.data(), id: doc.id }; // Map to match ProjectModel/InspectionModel equivalent
    });
  }

  return {
    list: projectList,
    hasMore,
    lastDocument:
      querySnapshot.docs.length > 0
        ? querySnapshot.docs[querySnapshot.docs.length - 1]
        : null,
  };
};

export const deleteProject = async (documentIds: any) => {
  try {
    const batch = writeBatch(db);

    documentIds.forEach((id: any) => {
      const docRef = doc(db, "projects", id);
      batch.delete(docRef);
    });

    await batch.commit();
    return true;
  } catch (e) {
    return false;
  }
};

export const exportProjects = async (payload: any) => {
  const idToken = localStorage.getItem("ScalarIdToken");

  const response: any = await fetch({
    url: `${host}/projects/export`,
    method: "POST",
    data: payload,
  });

  return response;
};
