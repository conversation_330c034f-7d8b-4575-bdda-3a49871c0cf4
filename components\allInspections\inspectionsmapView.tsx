// components/InspectionMap.tsx
import React, { useRef, useState } from "react";
import {
  GoogleMap,
  Marker,
  InfoWindow,
  useLoadScript,
} from "@react-google-maps/api";
import Image from "next/image";

const mapContainerStyle = {
  width: "100%",
  height: "100%",
  borderRadius: "12px",
};

const center = {
  lat: 45.0,
  lng: -10.0,
};

const mapOptions = {
  disableDefaultUI: true,
  zoomControl: true,
};

const grayMarker = "/images/home/<USER>";
const redMarker = "/images/home/<USER>";

interface Location {
  lat: number;
  lng: number;
}

interface Inspection {
  id: string;
  location: Location;
  completion: boolean;
  name: string;
  reportType: string;
  image: string;
}

interface InspectionMapProps {
  inspections: Inspection[];
  googleMapsApiKey: string | undefined;
  handleInspectionClick: Function;
}

const InspectionMap = ({
  inspections,
  googleMapsApiKey,
  handleInspectionClick,
}: any) => {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey, // Your Google Maps API Key
  });

  const mapRef = useRef<any>(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  const [selectedInspection, setSelectedInspection] =
    useState<Inspection | null>(null);

  if (loadError) return <div>Error loading map</div>;
  if (!isLoaded) return <div>Loading Map...</div>;

  return (
    <GoogleMap
      mapContainerStyle={mapContainerStyle}
      zoom={2}
      center={center}
      options={mapOptions}
    >
      {inspections.map((inspection: Inspection) => {
        if (inspection.location) {
          return (
            <Marker
              key={inspection.id}
              position={{
                lat: inspection.location.lat,
                lng: inspection.location.lng,
              }}
              icon={{
                url: inspection.completion ? grayMarker : redMarker, // Use custom markers
              }}
              onClick={() => setSelectedInspection(inspection)}
            />
          );
        }
      })}

      {selectedInspection && (
        <InfoWindow
          position={{
            lat: selectedInspection.location.lat,
            lng: selectedInspection.location.lng,
          }}
          onCloseClick={() => setSelectedInspection(null)}
        >
          <div
            onClick={() => handleInspectionClick(selectedInspection.id)}
            style={{ cursor: "pointer" }}
          >
            {/* Display the image and name */}
            <Image
              src={selectedInspection.image}
              alt={selectedInspection.name}
              width={200}
              height={100}
              className="w-[200px] h-[100px] object-contain bg-zinc-200 rounded-md"
            />
            <h1 className="text-[16px] leading-[20.8px] text-center font-[400] my-2">
              {selectedInspection.name}
            </h1>
          </div>
        </InfoWindow>
      )}
    </GoogleMap>
  );
};

export default InspectionMap;
