import { TextareaAutosize } from "@mui/material";
import {
  <PERSON>r,
  Input,
  Button,
  Tooltip,
  DatePicker,
  message,
  Skeleton,
} from "antd";
import dayjs from "dayjs";
import Image from "next/image";
import { Image as AntImage } from "antd";
import React, { useEffect, useRef, useState } from "react";
import Autocomplete from "react-google-autocomplete";
import moment from "moment";
import { fetchAPIResponse } from "@/src/services/newFindings.api";

const { TextArea } = Input;

const InspectionTypeFields = ({
  editableFindings,
  setEditableFindings,
  textareaRef,
  isAddingMeasure,
  isMeasureUpdate,
  setLocationModalOpen,
  isLocationSelected,
  isEdit,
  t,
  fieldImage,
  setFieldImage,
  fieldImageUrl,
  setFieldImageUrl,
  getTextSuggestions,
  suggestionResponse,
  textSuggestionLoader,
}: any) => {
  const inputRef = useRef<any>(null);

  const [apiResponses, setApiResponses] = useState<{ [key: string]: any }>({});
  const [apiLodaer, setApiLodaer] = useState(false);

  useEffect(() => {
    if (isAddingMeasure || isMeasureUpdate) {
      // Perform initial calculation based on the provided suggested values
      calculateFieldValues();
    }
  }, []);

  // useEffect(() => {}, [editableFindings]);

  const getAPIResponse = async (api_fields: any) => {
    try {
      setApiLodaer(true);
      const reportType = localStorage.getItem("reportTypeId");
      const inspection_id = localStorage.getItem("ScalarInspectionId");
      const findingTypeId = sessionStorage.getItem("FindingTypeId");
      const input_fields = editableFindings
        .filter((field: any) => api_fields.includes(field.id))
        .map((field: any) => {
          return {
            field_id: field.id,
            value_id: field.suggested_value,
            type: field.type,
          };
        })
        .filter((field: any) => field !== undefined);
      const payload = {
        input_fields: input_fields,
      };
      const api_response = await fetchAPIResponse(
        reportType,
        inspection_id,
        findingTypeId,
        payload
      );
      return api_response;
    } catch (error) {
      console.error("Error in getAPIResponse:", error);
      return "";
    } finally {
      setApiLodaer(false);
    }
  };

  const fetchAPI = async () => {
    const apiFields = editableFindings.filter(
      (field: any) => field.type === "api"
    );

    for (const field of apiFields) {
      try {
        const response = await getAPIResponse(field?.parameters?.input_fields);
        setApiResponses((prev) => ({
          ...prev,
          [field.id]: response,
        }));
      } catch (error) {
        console.error("Error fetching API response:", error);
      }
    }

    // After all API calls are complete, update editableFindings
    const updatedFindings = editableFindings.map((field: any) => {
      if (field.type === "api") {
        return {
          ...field,
          suggested_value: apiResponses[field.id]?.result?.value,
        };
      }
      return field;
    });

    setEditableFindings(updatedFindings);
  };

  // Function to calculate the value for fields with "type": "calculation"
  const calculateFieldValues = () => {
    const updatedFindings = [...editableFindings];
    updatedFindings.forEach((field: any, index: any) => {
      if (field.type === "calculation" && field.calculation) {
        const { fields: calculationFields, type } = field.calculation;
        if (type === "multiply") {
          const values = calculationFields.map((fieldName: string) => {
            const relatedField = updatedFindings.find(
              (f) => f.name === fieldName
            );
            return relatedField ? relatedField.suggested_value : 0;
          });
          const result = values.reduce(
            (acc: any, value: any) => acc * (value || 0),
            1
          );
          updatedFindings[index].suggested_value = result || null;
        }
      }
    });
    setEditableFindings(updatedFindings);
  };

  // Function for updating the suggested value of a field
  const handlePickerChange = (value: any, index: any, isParent = false) => {
    const updatedFindings = [...editableFindings];
    const changedField = updatedFindings[index];
    if (changedField.type === "date_picker") {
      console.log("value", value);
      const formattedValue = dayjs(value).format("DD/MM/YYYY");
      console.log("formattedValue", formattedValue);
      updatedFindings[index].suggested_value = formattedValue;
    } else {
      updatedFindings[index].suggested_value = value;
    }

    // Clear suggested_values of dependent fields
    updatedFindings.forEach((field) => {
      if (
        field.recalculation_parents &&
        field.recalculation_parents.length > 0
      ) {
        // Check if the changed field's id is in the recalculation_parents array
        const parentIds = field.recalculation_parents.map(
          (parent: any) => parent.id
        );
        if (parentIds.includes(changedField.id)) {
          field.suggested_value = "";
        }
      }
    });

    // Recursive function to update child fields
    const updateChildFields = (parentId: any) => {
      const childField = editableFindings.find(
        (f: any) => f.parent_field === parentId
      );

      if (childField) {
        if (childField?.type === "picker") {
          const childIndex = editableFindings.findIndex(
            (f: any) => f.id === childField.id
          );

          // Get the parent field to access its value
          // const parentField = editableFindings.find(
          //   (f: any) => f.id === parentId
          // );
          // const parentValue = parentField.suggested_value;

          // Get child options based on parent's value
          const childOptions = childField.options[value] || [];

          // Update the child's value to the first option in the new options
          const newChildValue =
            childOptions.length > 0 ? childOptions[0].id : "";
          updatedFindings[childIndex].suggested_value = newChildValue;
        }

        // Recursively update this child's children
        updateChildFields(childField.id);
      }
    };

    // Start the recursive update from the current field
    updateChildFields(updatedFindings[index].id);

    // If this is part of a calculation, trigger recalculation
    const relatedCalculation = editableFindings.find(
      (f: any) =>
        f.type === "calculation" &&
        f.calculation.fields.includes(updatedFindings[index].name)
    );
    if (relatedCalculation) {
      calculateFieldValues();
    }

    const clearDependentFields = (changedFieldId: string) => {
      updatedFindings.forEach((field) => {
        if (
          field.recalculation_parents &&
          field.recalculation_parents.length > 0
        ) {
          const recalculationParentIds = field.recalculation_parents.map(
            (parent: any) => parent.id
          );
          if (recalculationParentIds.includes(changedFieldId)) {
            if (field.type === "api") {
              fetchAPI();
            } else if (field.type === "text" || field.type === "number") {
              const suggestionResObj = suggestionResponse;
              suggestionResObj.suggestions.fields = updatedFindings;
              getTextSuggestions(suggestionResObj);
            }
          }
        }
      });

      // New recursive function to clear child values
      const clearChildrenValues = (parentId: string) => {
        const children = updatedFindings.filter(
          (f: any) => f.parent_field === parentId
        );
        if (children.length > 0) {
          children.forEach((child: any) => {
            clearDependentFields(child.id); // Recursively clear values of this child's children
            clearChildrenValues(child.id);
          });
        }
      };

      // Start the recursive clearing from the changed field
      clearChildrenValues(changedFieldId);
    };

    // Call the function with the changed field's ID
    clearDependentFields(changedField.id);

    setEditableFindings(updatedFindings);
  };

  // Prepare Cascader or Chip options
  const getCascaderOptions = (field: any) => {
    if (field.parent_field) {
      const parentField = editableFindings.find(
        (f: any) => f.id === field.parent_field
      );
      const parentValue = parentField?.suggested_value;

      if (parentValue && field.options[parentValue]) {
        return field.options[parentValue].map((option: any) => ({
          value: option.id,
          label: option.value,
        }));
      }
      return [];
    } else {
      return field?.options?.map((option: any) => ({
        value: option.id,
        label: option.value,
      }));
    }
  };

  // NEW: Function for handling multipicker value changes
  const handleMultipickerChange = (optionId: string, index: number) => {
    const updatedFindings = [...editableFindings];
    const field = updatedFindings[index];

    // Ensure suggested_value is always an array for multipicker fields
    if (!Array.isArray(field.suggested_value)) {
      field.suggested_value = field.suggested_value
        ? [field.suggested_value]
        : [];
    }

    // Toggle selection: add or remove the option from the array
    if (field.suggested_value.includes(optionId)) {
      field.suggested_value = field.suggested_value.filter(
        (id: string) => id !== optionId
      );
    } else {
      field.suggested_value = [...field.suggested_value, optionId];
    }

    const apiField = updatedFindings.find((item: any) => item.type === "api");
    if (apiField?.parameters?.input_fields?.includes(field.id)) {
      fetchAPI();
    }

    // Handle dependent fields if necessary
    const clearDependentFields = (changedFieldId: string) => {
      updatedFindings.forEach((field) => {
        if (
          field.recalculation_parents &&
          field.recalculation_parents.length > 0
        ) {
          const recalculationParentIds = field.recalculation_parents.map(
            (parent: any) => parent.id
          );
          if (recalculationParentIds.includes(changedFieldId)) {
            if (field.type === "api") {
              fetchAPI();
            } else if (field.type === "text" || field.type === "number") {
              const suggestionResObj = suggestionResponse;
              suggestionResObj.suggestions.fields = updatedFindings;
              getTextSuggestions(suggestionResObj);
            }
          }
        }
      });
    };

    clearDependentFields(field.id);
    setEditableFindings(updatedFindings);
  };

  // NEW: Handle Cascader selection for multipicker
  const handleMultipickerCascaderChange = (value: any, index: any) => {
    if (value && value.length > 0) {
      handleMultipickerChange(value[0], index);
    }
  };

  // NEW: Function to get visible options for multipicker
  const getVisibleMultipickerOptions = (field: any, options: any[]) => {
    if (!Array.isArray(field.suggested_value)) {
      field.suggested_value = field.suggested_value
        ? [field.suggested_value]
        : [];
    }

    // If we have 5 or fewer options total, show all options
    if (options.length <= 5) {
      return options;
    }

    // If selected options are 5 or more, show all selected options
    if (field.suggested_value.length >= 5) {
      return options.filter((option) =>
        field.suggested_value.includes(option.value)
      );
    }

    // Otherwise, show all selected options plus top options to make 5 total
    const selectedOptions = options.filter((option) =>
      field.suggested_value.includes(option.value)
    );

    const unselectedOptions = options.filter(
      (option) => !field.suggested_value.includes(option.value)
    );

    // Calculate how many unselected options we need to show
    const remainingSlots = 5 - selectedOptions.length;
    const topUnselectedOptions = unselectedOptions.slice(0, remainingSlots);

    // Combine and return
    return [...selectedOptions, ...topUnselectedOptions];
  };

  // Get remaining options for dropdown (those not shown as chips)
  const getRemainingOptions = (allOptions: any[], visibleOptions: any[]) => {
    const visibleOptionIds = visibleOptions.map((opt) => opt.value);
    return allOptions.filter((opt) => !visibleOptionIds.includes(opt.value));
  };

  // Handle chip button click
  const handleChipClick = (option: any, index: any) => {
    handlePickerChange(option, index);
  };

  // Handle Cascader selection
  const handleCascaderChange = (value: any, index: any) => {
    handlePickerChange(value[0], index);
  };

  const handleTemplateChange = (value: any, index: any) => {
    const updatedFindings = [...editableFindings];
    const changedField = updatedFindings[index];
    updatedFindings[index].suggested_value = value;
    setEditableFindings(updatedFindings);
  };

  const handleFieldImage = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const dataUrl = reader.result as string; // Convert the result to a string
        setFieldImageUrl(dataUrl);
      };
      reader.onerror = () => {
        message.error(t("Failed to read the file. Please try again."));
      };
      reader.readAsDataURL(file); // Use readAsDataURL for image source
    }
    setFieldImage(file);
  };

  // useEffect(() => {
  //   console.log('editableFindings', editableFindings)
  // }, [editableFindings])

  return (
    <div className="bg-white w-full px-10 rounded-[20px] mb-10">
      <div className="flex">
        {/* Labels + Divider + Inputs as Row-wise flex children */}
        <div className="flex flex-col w-full">
          {editableFindings.map((field: any, index: any) => {
            const isPicker =
              field.type === "picker" ||
              (field.options &&
                field.options.length > 0 &&
                field.type !== "multipicker" &&
                field.type !== "template");
            const isMultipicker = field.type === "multipicker";
            const isLocation = field.type === "location";
            const isText = field.type === "text";
            const isTemplate = field.type === "template";
            const isNumber = field.type === "number";
            const isCalculation = field.type === "calculation";
            const isAPI = field.type === "api";
            const isDatePicker = field.type === "date_picker";
            const imageField = field.type === "image_field";

            // Use the stored API response instead of calling in render
            if (isAPI) {
              field.suggested_value =
                apiResponses[field.id]?.result?.value || field.suggested_value;
            }

            // Get options for the field
            const options =
              isPicker ||
              isMultipicker ||
              (field.options && field.options.length > 0)
                ? getCascaderOptions(field)
                : [];

            // Make sure multipicker suggested_value is always an array
            if (isMultipicker && !Array.isArray(field.suggested_value)) {
              field.suggested_value = field.suggested_value
                ? [field.suggested_value]
                : [];
            }

            // For normal pickers
            if (isPicker && options && options.length > 0) {
              const value = options.find(
                (option: any) => option.value === field.suggested_value
              );
              if (!value && options.length > 0) {
                field.suggested_value = options[0].value;
              }
            }

            // Get visible options for multipicker fields
            const visibleMultipickerOptions = isMultipicker
              ? getVisibleMultipickerOptions(field, options)
              : [];

            // Get remaining options for dropdown
            const remainingMultipickerOptions = isMultipicker
              ? getRemainingOptions(options, visibleMultipickerOptions)
              : [];

            if (field?.conditional_field) {
              const parentValue = editableFindings.find(
                (f: any) => f.id === field.parent_field
              )?.suggested_value;
              if (Array.isArray(parentValue)) {
                if (!parentValue.includes(field?.conditional_parent_field_id)) {
                  return null; // Skip rendering this field if the condition is not met
                }
              } else if (field?.conditional_parent_field_id !== parentValue) {
                return null; // Skip rendering this field if the condition is not met
              }
            }

            return (
              <div key={index} className="flex w-full gap-4">
                {/* Label */}
                <div className="w-[15.5%] py-4 flex items-center justify-start text-left">
                  <label className="font-[600] text-[14px] leading-[16px]">
                    {field.name}
                    {field.required && (
                      <Tooltip
                        placement="right"
                        title="Required"
                        className="cursor-pointer"
                      >
                        <sup className="text-red-500">*</sup>
                      </Tooltip>
                    )}
                  </label>
                </div>

                {/* Divider */}
                <div className="border-r-2 border-gray-300 ml-4 h-full" />

                {/* Input */}
                <div
                  className={`w-[84.5%] py-4 ${
                    index !== 0 && "border-t-2"
                  } border-gray-300`}
                >
                  {isPicker && (
                    <div>
                      {options.length <= 5 ? (
                        <div className="flex flex-wrap gap-2">
                          {options.map((option: any) => (
                            <Button
                              key={option.value}
                              shape="round"
                              className={`${
                                field?.suggested_value === option.value
                                  ? "bg-blue-500 text-white"
                                  : "bg-white text-black"
                              } border min-w-[80px] h-[25px] text-[12px] p-0`}
                              style={
                                field?.suggested_value === option.value
                                  ? {
                                      backgroundColor: "rgb(59 130 246)",
                                      color: "white",
                                    }
                                  : {}
                              }
                              onClick={() =>
                                handleChipClick(option.value, index)
                              }
                            >
                              {option.label}
                            </Button>
                          ))}
                        </div>
                      ) : (
                        <>
                          <div className="flex flex-wrap gap-2">
                            {options.slice(0, 5).map((option: any) => (
                              <Button
                                key={option.value}
                                shape="round"
                                className={`${
                                  field?.suggested_value === option.value
                                    ? "bg-blue-500 text-white"
                                    : "bg-white text-black"
                                } border min-w-[80px] h-[25px] text-[12px] p-0`}
                                style={
                                  field?.suggested_value === option.value
                                    ? {
                                        backgroundColor: "rgb(59 130 246)",
                                        color: "white",
                                      }
                                    : {}
                                }
                                onClick={() =>
                                  handleChipClick(option.value, index)
                                }
                              >
                                {option.label}
                              </Button>
                            ))}
                          </div>
                          <Cascader
                            options={options.map((option: any) => ({
                              value: option.id,
                              label: option.value,
                            }))}
                            onChange={(value: any) =>
                              handleCascaderChange(value, index)
                            }
                            value={field?.suggested_value}
                            placeholder="Select an option"
                            className="mt-2 w-full h-[25px] placeholder-black"
                            dropdownRender={(menus) => (
                              <div
                                style={{
                                  maxWidth: "600px",
                                  maxHeight: "200px",
                                  overflowY: "auto",
                                }}
                              >
                                {menus}
                              </div>
                            )}
                          />
                        </>
                      )}
                    </div>
                  )}

                  {/* NEW: Multipicker field type */}
                  {isMultipicker && (
                    <div>
                      {/* Display visible options as chips */}
                      <div className="flex flex-wrap gap-2 mb-2">
                        {visibleMultipickerOptions.map((option: any) => (
                          <Button
                            key={option.value}
                            shape="round"
                            className={`${
                              Array.isArray(field.suggested_value) &&
                              field.suggested_value.includes(option.value)
                                ? "bg-blue-500 text-white"
                                : "bg-white text-black"
                            } border min-w-[80px] h-[25px] text-[12px] p-0`}
                            style={
                              Array.isArray(field.suggested_value) &&
                              field.suggested_value.includes(option.value)
                                ? {
                                    backgroundColor: "rgb(59 130 246)",
                                    color: "white",
                                  }
                                : {}
                            }
                            onClick={() =>
                              handleMultipickerChange(option.value, index)
                            }
                          >
                            {option.label}
                          </Button>
                        ))}
                      </div>

                      {/* Show dropdown for remaining options if any */}
                      {remainingMultipickerOptions.length > 0 && (
                        <Cascader
                          options={remainingMultipickerOptions || []}
                          onChange={(value: any) =>
                            handleMultipickerCascaderChange(value, index)
                          }
                          allowClear={false}
                          showSearch
                          value={[t("Select more options")]}
                          placeholder={t("Select more options")}
                          multiple={false}
                          className="my-2 w-full h-[25px] placeholder-black"
                          dropdownRender={(menus) => (
                            <div
                              style={{
                                maxWidth: "600px",
                                maxHeight: "200px",
                                overflowY: "auto",
                              }}
                              className="text-[12px]"
                            >
                              {menus}
                            </div>
                          )}
                        />
                      )}
                    </div>
                  )}

                  {isTemplate && (
                    <div>
                      {options.length <= 5 ? (
                        <div className="flex flex-wrap gap-2">
                          {options.map((option: any) => (
                            <Button
                              key={option.value}
                              shape="round"
                              className={`${
                                field?.suggested_value === option.value
                                  ? "bg-blue-500 text-white"
                                  : "bg-white text-black"
                              } border min-w-[80px] h-[25px] text-[12px] p-0`}
                              style={
                                field?.suggested_value === option.value
                                  ? {
                                      backgroundColor: "rgb(59 130 246)",
                                      color: "white",
                                    }
                                  : {}
                              }
                              disabled={isEdit}
                              onClick={() =>
                                handleTemplateChange(option.value, index)
                              }
                            >
                              {option.label}
                            </Button>
                          ))}
                        </div>
                      ) : (
                        <>
                          <div className="flex flex-wrap gap-2">
                            {options.slice(0, 5).map((option: any) => (
                              <Button
                                key={option.value}
                                shape="round"
                                className={`${
                                  field?.suggested_value === option.value
                                    ? "bg-blue-500 text-white"
                                    : "bg-white text-black"
                                } border min-w-[80px] h-[25px] text-[12px] p-0`}
                                style={
                                  field?.suggested_value === option.value
                                    ? {
                                        backgroundColor: "rgb(59 130 246)",
                                        color: "white",
                                      }
                                    : {}
                                }
                                onClick={() =>
                                  handleTemplateChange(option.value[0], index)
                                }
                              >
                                {option.label}
                              </Button>
                            ))}
                          </div>
                          <Cascader
                            options={options.map((option: any) => ({
                              value: option.id,
                              label: option.value,
                            }))}
                            onChange={(value: any) =>
                              handleCascaderChange(value, index)
                            }
                            value={field?.suggested_value}
                            placeholder="Select an option"
                            className="mt-2 w-full h-[25px] placeholder-black"
                            dropdownRender={(menus) => (
                              <div
                                style={{
                                  maxWidth: "600px",
                                  maxHeight: "200px",
                                  overflowY: "auto",
                                }}
                              >
                                {menus}
                              </div>
                            )}
                          />
                        </>
                      )}
                    </div>
                  )}

                  {isLocation && (
                    <div className="w-full flex items-center gap-2">
                      <Autocomplete
                        apiKey={process.env.NEXT_GOOGLE_PLACE_API}
                        className="w-[300px] md:w-[355px] mt-2 py-1 pr-1 pl-8 md:py-2 md:pr-2 md:ml-[5px] border bg-white text-black rounded focus:outline-none focus-visible:outline-none hidden"
                      />
                      <Button
                        shape="round"
                        onClick={() => setLocationModalOpen(true)}
                        type="primary"
                        className="gap-3 h-[30px] text-[12px] px-4 custom-button border-opacity-30 bg-[#2F80ED] text-white"
                      >
                        <Image
                          width={15}
                          height={15}
                          alt="logo"
                          src="/images/newInspection/location.png"
                          className="w-[15px] h-[15px]"
                        />
                        <p className="text-white text-[12px] font-[400]">
                          {t("Select Location")}
                        </p>
                      </Button>
                      {field.suggested_value && (
                        <div className="flex items-center gap-1">
                          <Image
                            width={18}
                            height={18}
                            alt=""
                            src="/images/newInspection/check.png"
                            className="w-[18px] h-[18px]"
                          />
                          <h1 className="text-[#2F80ED] font-[500] mt-[2px]">
                            {t("Selected")}
                          </h1>
                        </div>
                      )}
                    </div>
                  )}

                  {/* {isText && (
                    <TextArea
                      ref={textareaRef}
                      value={field.suggested_value || ""}
                      onChange={(e) =>
                        handlePickerChange(e.target.value, index)
                      }
                      className="min-h-[36px] text-area-auto border p-3 px-4 text-pretty leading-[16.9px] font-[400] rounded-[10px] w-full resize-none overflow-auto"
                      autoSize={{ minRows: 1, maxRows: 5 }}
                      allowClear
                    />
                  )} */}

                  {isText && (
                    <>
                      {textSuggestionLoader ? (
                        <div className="w-full flex justify-center">
                          <Skeleton.Input active block />
                        </div>
                      ) : (
                        <TextArea
                          ref={textareaRef}
                          value={field.suggested_value}
                          onChange={(e) =>
                            handlePickerChange(e.target.value, index)
                          }
                          className={`text-area-auto text-[12px] border text-pretty leading-[16.9px] font-[400] rounded-[10px] w-full resize-none outline-1 outline-white overflow-auto scrollbar-textArea transition-all`}
                          autoSize={{ minRows: 1, maxRows: 5 }}
                          allowClear
                        />
                      )}
                    </>
                  )}

                  {isNumber && (
                    <Input
                      type="number"
                      value={field.suggested_value}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (!value || parseFloat(value) >= 0) {
                          handlePickerChange(value, index);
                        }
                      }}
                      className="px-3 min-h-[36px] py-2 border rounded-[10px] w-full no-arrows"
                      allowClear
                    />
                  )}

                  {isCalculation && (
                    <Input
                      type="number"
                      value={field.suggested_value}
                      disabled
                      className="px-3 py-2 border rounded-[10px] w-full no-arrows"
                    />
                  )}

                  {isAPI && (
                    <>
                      {apiLodaer ? (
                        <div className="w-full flex justify-center">
                          <Skeleton.Input active block />
                        </div>
                      ) : (
                        <Button
                          shape="round"
                          disabled
                          className={`bg-blue-500 text-white border min-w-[80px] h-[25px] text-[12px] p-0`}
                          style={{
                            backgroundColor: "rgb(59 130 246)",
                            color: "white",
                          }}
                        >
                          {field.suggested_value}
                        </Button>
                      )}
                    </>
                  )}

                  {isDatePicker && (
                    <DatePicker
                      value={
                        field.suggested_value &&
                        /^\d{2}\/\d{2}\/\d{4}$/.test(field.suggested_value)
                          ? dayjs(field.suggested_value, "DD/MM/YYYY")
                          : null
                      }
                      format="DD/MM/YYYY"
                      // disabledDate={(current) =>
                      //   current && current > dayjs().endOf("day")
                      // }
                      // disabled={disabledField.includes(field.id)}
                      onChange={(date) => handlePickerChange(date, index)}
                      className="w-full h-[30px] text-[12px] rounded-[10px] border px-3 py-2"
                    />
                  )}

                  {imageField && (
                    <div className="flex gap-2 items-center">
                      <Button
                        onClick={() => inputRef.current.click()}
                        shape="round"
                        type="primary"
                        className={`custom-button h-[30px] text-[12px] px-4 border-opacity-30 bg-[#2F80ED] text-white`}
                      >
                        <AntImage
                          width={15}
                          height={15}
                          alt="logo"
                          src={"/images/newInspection/image.png"}
                          className="w-[15px] h-[15px]"
                          preview={false}
                        />
                        <p className={`text-white text-[12px] font-[400]`}>
                          {fieldImage !== null || fieldImageUrl !== ""
                            ? t("Replace Image")
                            : t("Select Image")}
                        </p>
                        <input
                          type="file"
                          ref={inputRef}
                          onChange={handleFieldImage}
                          accept="image/*"
                          className="hidden"
                          name=""
                          id=""
                        />
                      </Button>
                      {(fieldImage !== null || fieldImageUrl !== "") && (
                        <div className="flex items-center gap-2">
                          <AntImage
                            width={30}
                            height={30}
                            alt=""
                            src={fieldImageUrl}
                            className="w-[30px] h-[30px] rounded-md object-contain border cursor-pointer"
                            preview={{ mask: false, toolbarRender: () => null }}
                          />
                          <Tooltip title={t("Delete")} placement="right">
                            <Image
                              src="/images/home/<USER>"
                              width={24}
                              height={24}
                              alt="image"
                              className="w-[24px] h-[24px] object-cover z-10 bg-white rounded-lg cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                setFieldImage(null);
                                setFieldImageUrl("");
                              }}
                            />
                          </Tooltip>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default InspectionTypeFields;
