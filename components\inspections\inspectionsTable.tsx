import React, { useEffect } from "react";
import { Divider, Radio, Table } from "antd";
import moment from "moment";
import { Timestamp } from "firebase/firestore";
import Image from "next/image";
import { useTranslation } from "react-i18next";

const InspectionsTable = ({
  searchedInspections,
  inspectionTypeList,
  setDeleteId,
  setIsModalOpen,
  handleInspectionClick,
  selectedInspections,
  setSelectedInspections,
}: any) => {

  const { t } = useTranslation();

  const formatTimestamp = (timestamp: Timestamp) => {
    const milliseconds =
      timestamp.seconds * 1000 + timestamp.nanoseconds / 1000000;
    const parsedDate = moment(milliseconds);
    return parsedDate.format("D MMM YYYY");
  };
  const getInspectionTypeLabel = (record: any) => {
    return (
      inspectionTypeList
        ?.filter((item: any) => item.value === record.id)
        .map((item: any) => item.label)[0] || ""
    );
  };

  const columns: any = [
    {
      title: <>{t("Inspection Name")}</>,
      dataIndex: "name",
      render: (name: any) => <p>{name.slice(0,1).toUpperCase()+name.slice(1).toLowerCase()}</p>,
      sorter: (a: any, b: any) => a.name.localeCompare(b.name), // Use localeCompare for string sorting
      sortDirections: ["ascend", "descend"],
    },
    // {
    //   title: <>{t("Inspection Type")}</>,
    //   dataIndex: "reporttype",
    //   render: (record: any) => <p>{getInspectionTypeLabel(record)}</p>,
    //   sorter: (a: any, b: any) =>
    //     a.id.localeCompare(b.id),
    //   sortDirections: ["ascend", "descend"],
    //   // sorter: (a: any, b: any) =>
    //   //   getInspectionTypeLabel(a).localeCompare(getInspectionTypeLabel(b)), // Sort by inspection type label
    //   // sortDirections: ["ascend", "descend"],
    // },
    {
      title: <>{t("Status")}</>,
      render: (record: any) => <p>{record.isCompleted ? <>{t("Done")}</> : <>{t("Todo")}</>}</p>,
    },
    // {
    //   title: <>{t("Project")}</>,
    //   dataIndex: "project",
    //   render: (record: any) => <p>{record.id}</p>,
    // },
    {
      title: <>{t("Date")}</>,
      render: (record: any) => <p>{formatTimestamp(record.created_at)}</p>,
      sorter: (a: any, b: any) => {
        const aMilliseconds =
          a.created_at.seconds * 1000 + a.created_at.nanoseconds / 1000000;
        const bMilliseconds =
          b.created_at.seconds * 1000 + b.created_at.nanoseconds / 1000000;
        return aMilliseconds - bMilliseconds; // Sort by date (numerical comparison)
      },
      sortDirections: ["ascend", "descend"],
    },
    {
      title: <>{t("Action")}</>,
      width: 150,
      fixed: "right",
      render: (_: any, record: any) => (
        <button
          className="flex gap-2 text-red-500"
          onClick={(e) => {
            e.stopPropagation();
            setDeleteId(record.id);
            setIsModalOpen(true);
          }}
          aria-label="delete"
        >
          <Image
            src="/images/home/<USER>"
            alt="logo"
            width={24}
            height={24}
            className="w-[24px] h-[24px]"
          />{" "}
          {t("Delete")}
        </button>
      ),
      // Apply the background color conditionally to the entire cell
      onCell: (record: any) => {
        return {
          style: {
            backgroundColor:
              record.isCompleted && !selectedInspections.includes(record.id)
                ? "#e5e5e5"
                : "",
          },
        };
      },
    },
  ];

  const rowSelection: any["rowSelection"] = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      setSelectedInspections(selectedRowKeys);
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.name === "Disabled User", // Column configuration not to be checked
      name: record.name,
    }),
  };

  return (
    <Table
      rowKey="id"
      rowSelection={{ type: "checkbox", ...rowSelection }}
      columns={columns}
      dataSource={searchedInspections}
      pagination={false}
      className="mb-10"
      rowClassName={(record: any) =>
        record.isCompleted ? "bg-[#e5e5e5] no-hover" : ""
      }
      onRow={(record) => ({
        onClick: () => handleInspectionClick(record.id, record.name, record?.finding_order),
        className: "cursor-pointer",
      })}
      sticky
    />
  );
};

export default InspectionsTable;
