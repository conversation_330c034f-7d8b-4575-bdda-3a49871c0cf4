import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import {
    getAuth,
    setPersistence,
    browserLocalPersistence,
} from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { message } from "antd";

const database = process.env.NEXT_DB;

const firebaseConfig = {
    apiKey: "AIzaSyA8bigxulnNNFyFPcnAOWOBApPCMnBuBxU",
    authDomain: "scalar-inspect.firebaseapp.com",
    databaseURL: "https://scalar-inspect-default-rtdb.firebaseio.com",
    projectId: "scalar-inspect",
    storageBucket: "scalar-inspect",
    messagingSenderId: "739231527227",
    appId: "1:739231527227:web:d114dcb621bc5c85c9471e",
    measurementId: "G-D0MHYSZ1T1",
};

const app: any = initializeApp(firebaseConfig);
const analytics = typeof window !== "undefined" ? getAnalytics(app) : null;
const auth = getAuth(app);

// Set session persistence
setPersistence(auth, browserLocalPersistence).catch((error) => {
    console.error("Error setting persistence:", error);
});

const db =
    database === "development"
        ? getFirestore(app, "deve")
        : database === "staging"
            ? getFirestore(app, "staging")
            : getFirestore(app, "prod")

const storage = getStorage(app);

const refreshToken = async (): Promise<string> => {
    return new Promise((resolve, reject) => {
        const user = auth.currentUser;
        if (user) {
            user
                .getIdToken(true)
                .then((token) => {
                    localStorage.setItem("ScalarIdToken", token);
                    resolve(token);
                })
                .catch((error) => {
                    reject(error);
                });
        } else {
            reject(new Error("No user is currently signed in."));
        }
    });
};

export { auth, db, storage, refreshToken };
