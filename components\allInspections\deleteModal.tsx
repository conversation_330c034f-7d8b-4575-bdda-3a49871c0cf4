"use client";

import React, { useState } from "react";
import { Poppins } from "next/font/google";
import { useTranslation } from "react-i18next";
import { Button, Modal as AntdModal, message } from "antd";
import { deleteInspections } from "@/src/services/allInspections.api";

const poppins = Poppins({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

interface DeleteModalProps {
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
  setMenu: (open: boolean) => void;
  selectedInspectionObj: any;
  setSearchedInspections: any;
  setRefresh: (cb: (prev: any) => any) => void;
}

const DeleteModal: React.FC<DeleteModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  selectedInspectionObj,
  setRefresh,
  setMenu,
  setSearchedInspections
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const deleteSelectedInspections = async () => {
    try {
      setLoading(true);
      if (selectedInspectionObj.length > 0) {
        const res = await deleteInspections(selectedInspectionObj);

        if (res) {
          message.success(t("Successfully deleted all inspections."));
          // setRefresh((prev: any) => !prev);
        }

        const selectedInspection = selectedInspectionObj.map((insp: any) => insp?.id)

        setSearchedInspections((prev: any) =>
          prev.filter(
            (inspection: any) =>
              !selectedInspection.includes(inspection.id)
          )
        );
      }
    } catch (error) {
      message.error(t("Something went wrong, try again later!"));
    } finally {
      setLoading(false);
      setIsModalOpen(false);
      setMenu(false);
    }
  };

  return (
    <AntdModal
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      centered
      className={`${poppins.className} custom-delete-modal`}
      width={350}
    >
      <div className="relative">
        <div className="mt-6">
          <h1 className="text-left text-[25px] leading-[52.08px] font-[500]">
            {t("Delete Inspection!")}
          </h1>
          <p className="text-[18px] text-left mt-2">
            {t("Are you sure you want to")} <br />
            {t("delete Inspection?")}
          </p>

          <div className="text-center flex justify-between gap-4 mt-10">
            <Button
              type="default"
              className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] bg-[#ff910020] hover:bg-[#ff910015]"
              onClick={() => setIsModalOpen(false)}
            >
              {t("Cancel")}
            </Button>
            <Button
              type="primary"
              className="w-[50%] h-[45px] text-[14px] border-none"
              style={{
                backgroundColor: "#FF9200",
                color: "white",
              }}
              onClick={deleteSelectedInspections}
              loading={loading}
            >
              {t("Delete")}
            </Button>
          </div>
        </div>
      </div>
    </AntdModal>
  );
};

export default DeleteModal;
