'use client';

import React from 'react';
import { Mo<PERSON>, Button } from 'antd';
import { Poppins } from 'next/font/google';
import { useTranslation } from 'react-i18next';

const poppins = Poppins({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
});

interface LogoutModalProps {
  handleLogout: (e: React.FormEvent<HTMLFormElement>) => void;
  isModalOpen: boolean;
  setIsModalOpen: (val: boolean) => void;
}

const LogoutModal: React.FC<LogoutModalProps> = ({
  handleLogout,
  isModalOpen,
  setIsModalOpen,
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      centered
      className={`${poppins.className} custom-logout-modal`}
      width={350}
    >
      <form onSubmit={handleLogout} className="relative">

        <div className="text-center">
          <h1 className="mt-4 text-[25px] leading-[52.08px] font-[600]">
            {t('Logout')}!
          </h1>
          <p className="text-[16px] mt-1">{t('Are you sure you want to Logout?')}</p>

          <div className="flex justify-between gap-4 mt-10">
            <Button
              type="default"
              className="w-[140px] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] bg-[#ff910020] hover:bg-[#ff910015]"
              onClick={() => setIsModalOpen(false)}
            >
              {t('Cancel')}
            </Button>
            <Button
              htmlType="submit"
              type="primary"
              className="w-[140px] h-[45px] text-[14px] border-none"
              style={{ backgroundColor: '#FF9200', color: 'white' }}
            >
              {t('Logout')}
            </Button>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default LogoutModal;
