"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Poppins } from "next/font/google";
import { exportProjects } from "@/src/services/project.api";
import { Modal as AntdModal, But<PERSON>, message } from "antd";

const poppins = Poppins({ weight: "400", subsets: ["latin"] });

interface ExportModalProps {
  setIsModalOpen: (val: boolean) => void;
  setMenu: (val: boolean) => void;
  isModalOpen: boolean;
  selectedProjects: string[];
}

const ExportModal: React.FC<ExportModalProps> = ({
  setIsModalOpen,
  isModalOpen,
  selectedProjects,
  setMenu,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const exportSelectedProjects = async () => {
    try {
      setLoading(true);
      if (selectedProjects.length > 0) {
        const payload = {
          user_id: localStorage.getItem("ScalarUserId"),
          project_ids: selectedProjects,
        };
        const res = await exportProjects(payload);
        if (res.project_ids.length === selectedProjects.length) {
          message.success(t("Successfully exported all projects."));
        } else {
          message.warning(t("Some projects failed to export."));
        }
      }
      setIsModalOpen(false);
      setMenu(false);
    } catch (error) {
      message.error(
        t("Something went wrong while exporting, try again later.")
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <AntdModal
      open={isModalOpen}
      onCancel={() => {
        setIsModalOpen(false);
      }}
      footer={null}
      centered
      className={`${poppins.className} custom-export-projects-modal`}
      width={350}
    >
      <div className="relative">
        <div className="mt-4">
          <h1 className="text-left text-[25px] leading-[52.08px] font-[600]">
            {t("Export")} {t("Projects")}!
          </h1>
          <p className="text-[18px] text-left mt-1">
            {t("Are you sure you want to Export")} <br />
            {t("Projects")}?
          </p>

          <div className="text-center flex justify-between gap-4 mt-10">
            <Button
              type="default"
              className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] bg-[#ff910020] hover:bg-[#ff910015]"
              onClick={() => setIsModalOpen(false)}
            >
              {t("Cancel")}
            </Button>
            <Button
              type="primary"
              className="w-[50%] h-[45px] text-[14px] border-none"
              style={{
                backgroundColor: "#FF9200",
                color: "white",
              }}
              loading={loading}
              onClick={exportSelectedProjects}
            >
              {t("Export")}
            </Button>
          </div>
        </div>
      </div>
    </AntdModal>
  );
};

export default ExportModal;
