import React, { useState } from "react";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { <PERSON><PERSON>ip, Spin, Popover, Image } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { getMultiPickerSuggestedValue } from "../newInspection/utils";

const poppins = Poppins({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

interface FindingRowProps {
  finding: any;
  parentId: string;
  onFindingClick: (findingId: string, parentId: string) => void;
  // onImageClick: (src: string, isVideo: boolean) => void;
  groupedFindings: any[];
  maxFieldCount: number;
  decompFields: any[];
  fieldNames: string[]
}

const FindingRow: React.FC<FindingRowProps> = ({
  finding,
  parentId,
  decompFields,
  onFindingClick,
  groupedFindings,
  maxFieldCount,
  fieldNames
}) => {
  // Track loading state for each media item
  const [loadingStates, setLoadingStates] = useState<Record<number, boolean>>(
    finding.media?.reduce(
      (acc: Record<number, boolean>, _: any, idx: number) => {
        acc[idx] = true;
        return acc;
      },
      {}
    ) || {}
  );

  // Parse the finding fields
  const findingFields = finding.fields ? JSON.parse(finding.fields) : [];

  // Get media for the finding
  const findingMedia = finding.media || [];

  // Function to handle image load completion
  const handleImageLoad = (index: number) => {
    setLoadingStates((prev) => ({
      ...prev,
      [index]: false,
    }));
  };

  // Find the measures count for this finding
  const getMeasureCount = () => {
    // Find the group in groupedFindings
    const group = groupedFindings.find((g) => g.id === parentId);
    if (!group) return 0;

    // Count measures that have this finding as parent
    return group.decompositionItem.filter(
      (item: any) => item.parent_finding_id === finding.id
    ).length;
  };

  const measureCount = getMeasureCount();

  // Improved getFieldValue to handle any field index
  const getFieldValueByName = (fieldName: string) => {
    if (!findingFields || !findingFields.length) return null;

    const field = findingFields.find((f: any) => f.name === fieldName);
    if (
      !field ||
      field?.suggested_value === undefined ||
      field?.suggested_value === null
    ) {
      return null;
    }

    if (
      field?.type === "parent_api" ||
      field?.type === "api" ||
      field?.type === "text" ||
      field?.type === "number" ||
      field?.type === "calculation" ||
      field?.type === "date_picker"
    ) {
      if (
        typeof field?.suggested_value === "string" ||
        typeof field?.suggested_value === "number"
      ) {
        return {
          name: field?.name,
          value: field?.suggested_value || null,
        };
      } else {
        return {
          name: field?.name,
          value: JSON.stringify(field?.suggested_value) || null,
        };
      }
    }

    if (field?.type === "image_field") {
      return {
        name: field?.name,
        value: field?.suggested_value
          ? {
              src: field?.suggested_value,
              isImagePicker: true,
            }
          : null,
      };
    }

    if (field?.type === "multipicker") {
      return {
        name: field?.name,
        value: getMultiPickerSuggestedValue(field)?.toString() || null,
      };
    }

    if (field?.type === "picker") {
      if (Array.isArray(field?.options)) {
        return {
          name: field?.name,
          value: field?.options
            ?.find((option: any) => option.id === field?.suggested_value)
            ?.value?.toString(),
        };
      }
      if (typeof field?.options === "object") {
        const parentFieldValue = findingFields.find(
          (f: any) => f.id === field?.parent_field
        )?.suggested_value;
        if (parentFieldValue) {
          const optionsArray = field?.options[parentFieldValue];
          return {
            name: field?.name,
            value: optionsArray
              ?.find((option: any) => option.id === field?.suggested_value)
              ?.value?.toString(),
          };
        } else {
          const decompositionParentValue = decompFields.find(
            (f: any) => f.id === field?.parent_field
          )?.suggested_value;
          if (decompositionParentValue) {
            const optionsArray = field?.options[decompositionParentValue];
            return {
              name: field?.name,
              value: optionsArray
                ?.find((option: any) => option.id === field?.suggested_value)
                ?.value?.toString(),
            };
          }
        }
      }
      return {
        name: field?.name,
        value: field?.suggested_value?.toString(),
      };
    }
  };

  return (
    <div
      className={`grid px-2 py-1 border-t hover:bg-gray-50 cursor-pointer ${poppins.className}`}
      style={{
        gridTemplateColumns: `minmax(100px, 1fr) repeat(${
          maxFieldCount - 1
        }, minmax(100px, 1fr))`,
      }}
      onClick={() => onFindingClick(finding.id, parentId)}
    >
      <Popover
        title={finding.title}
        autoAdjustOverflow
        overlayInnerStyle={{
          maxWidth: "300px",
          maxHeight: "300px",
          overflow: "auto",
          textAlign: "center",
        }}
        arrow={false}
      >
        <div className="truncate pl-4">{finding.title}</div>
      </Popover>

      {/* Dynamically render field values */}
      {/* {Array.from({ length: maxFieldCount })
        .slice(1)
        .map((_, i) => {
          const fieldValue: any = getFieldValue(i + 1); // Adjust index to account for removed first element
          if (fieldValue) {
            return (
              <Popover
                key={i}
                title={fieldValue?.name}
                content={fieldValue?.value || "-"}
                autoAdjustOverflow
                overlayInnerStyle={{
                  maxWidth: "300px",
                  maxHeight: "300px",
                  overflow: "auto",
                }}
                arrow={false}
              >
                <div className="truncate text-left px-4">
                  {fieldValue?.value === 0 ? 0 : fieldValue?.value || "-"}
                </div>
              </Popover>
            );
          } else {
            return (
              <div key={i} className="truncate text-left px-4">
                -
              </div>
            );
          }
        })} */}
        {fieldNames.map((fieldName, i) => {
        const fieldValue = getFieldValueByName(fieldName);

        if (fieldValue?.value) {
          if (fieldValue?.value?.isImagePicker) {
            return (
              <Image
              key={i}
                src={fieldValue?.value?.src}
                alt={"-"}
                width={30}
                height={30}
                className="w-[30px] h-[30px] rounded-md object-contain border cursor-pointer"
                preview={{ mask: false, toolbarRender: () => null }}
              />
            )
          }
          return (
            <Popover
              key={i}
              title={fieldName}
              content={fieldValue?.value || "-"}
              autoAdjustOverflow
              overlayInnerStyle={{
                maxWidth: "300px",
                maxHeight: "300px",
                overflow: "auto",
              }}
              arrow= {false}
            >
              <div className="truncate text-left px-4 pt-[2px]">
                {fieldValue?.value || "-"}
              </div>
            </Popover>
          );
        } else {
          return (
            <div key={i} className="truncate text-left px-4 pt-[2px]">
              {fieldValue?.value || "-"}
            </div>
          );
        }
      })}
    </div>
  );
};

export default FindingRow;
