"use client";
import React, { useEffect, useRef, useState } from "react";
import Navbar from "@/components/Navbar/navbar";
import Sidebar from "@/components/sidebar/sidebar";
import { <PERSON><PERSON>, <PERSON><PERSON>s, Open_Sans } from "next/font/google";
import Image from "next/image";
// import { inspectionTypeList } from "@/src/libs/constants";
import {
  Select,
  DatePicker,
  ConfigProvider,
  Input,
  message,
  Button,
  Tooltip,
} from "antd";
import moment, { Moment } from "moment";
import "moment/locale/en-gb";
import "moment/locale/nl";
import enLocale from "antd/es/date-picker/locale/en_GB";
import nlLocale from "antd/es/date-picker/locale/nl_NL";
import {
  addInspectionDetail,
  fetchInspection,
  updateInspectionDetails,
  generateTypeFields,
  handleUpload,
  deleteFile,
  fetchAllFindings,
  addFindingsForInspection,
  generateSuggestionFieldsFields,
} from "@/src/services/inspectionDetails.api";
import { getUserReportTypes } from "@/src/services/auth.api";
import { doc, Timestamp } from "firebase/firestore";
import { useRouter, useSearchParams } from "next/navigation";
import { useLoaderContext } from "@/context/LoaderContext";
import Loader from "@/components/loader/loader";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import Radio from "@mui/material/Radio";
import dayjs from "dayjs";
import InspectionTypeFields from "./inspectionTypeFields";
import LoadingCards from "./loadingCards";
import LocationModal from "./locationModal";
import imageCompression from "browser-image-compression";
import Media from "./mediaModal";
import { useSidebarContext } from "@/context/sidebarContext";
import { fetchTextSuggestions } from "@/src/services/newInspection.api";
import { fetchAllDocuments as fetchAllProjectDoc } from "@/src/services/project.api";
import { RightOutlined } from "@ant-design/icons";
import { db } from "@/firebase.config";

const locales: any = {
  en: { momentLocale: "en-gb", antdLocale: enLocale },
  nl: { momentLocale: "nl", antdLocale: nlLocale },
};

type Finding = {
  id: string;
  parent_finding_id: string | null;
  field_values: Record<string, string>;
  media: { isVideo: boolean; url: string }[];
  resize_media: { isVideo: boolean; url: string }[];
  title_fields: string[];
  children?: Finding[]; // Children property to store nested findings
};

const { Option } = Select;

const poppins = Poppins({ weight: ["300", "400", "500", "700"], subsets: ["latin"] });
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const OpenSansNor = Open_Sans({ weight: "500", subsets: ["latin"] });
const OpenSans = Open_Sans({ weight: "700", subsets: ["latin"] });

const InspectionMetaData = () => {
  const router = useRouter();
  const { isCollapsed } = useSidebarContext();
  const [isEdit, setIsEdit] = useState(false);
  const [inspectionType, setInspectionType] = useState<any>(null);
  const [inspectionName, setInspectionName] = useState(String);
  const [dateAndTime, setDateAndTime] = useState<any | null>(null);
  const [momentDate, setMomentDate] = useState<any>(null);
  const [searchInspection, setSearchInspection] = useState<any>(null);
  const [inspectionTypeList, setInspectionTypeList] = useState<any>(null);
  const [locale, setLocale] = useState<any>();
  const [loadingCard, setLoadingCard] = useState(false);
  const [typeFields, setTypeFields] = useState<any>([]);
  const textareaRef: any = useRef(null);
  const [locationModalOpen, setLocationModalOpen] = useState(false);
  const [location, setLocation] = useState<{ lat: number; lon: number } | null>(
    null
  );
  const inputRef = useRef<any>(null);
  const [typeFieldLoader, setTypeFieldLoader] = useState(false);
  const [inspectionImage, setInspectionImage] = useState<any>(null);
  const [imageUrl, setImageUrl] = useState<any>("");
  const [deleteImage, setDeleteImage] = useState("");
  const [showMedia, setShowMedia] = useState(false);
  const { loader, setLoader } = useLoaderContext();
  const [updateImage, setUpdateImage] = useState(false);
  const { t } = useTranslation();

  const [decompositionFields, setDecompositionFields] = useState<any>(null);
  const [findingFields, setFindingFields] = useState<any>(null);
  const [measureFields, setMeasureFields] = useState<any>(null);

  const [fieldImageUrl, setFieldImageUrl] = useState<any>("");
  const [fieldImage, setFieldImage] = useState<any>(null);

  const [suggestionResponse, setSuggestionResponse] = useState<any>(null);
  const [textSuggestionLoader, setTextSuggestionLoader] = useState(false);

  const searchParams: any = useSearchParams();
  const [prevPath, setPrevPath] = useState("");

  useEffect(() => {
    localStorage.removeItem("newAdded");
    localStorage.removeItem("imageSource");
    setPrevPath(searchParams.get("prev"));
  }, []);

  const [selectedProject, setSelectedProject] = useState<any | null>(null);
  const [Projects, setProjects] = useState<any[]>([]);
  const [allProjectsOptions, setAllProjectsOptions] = useState<any[]>([]);

  const firstRender = useRef<any>(true);

  useEffect(() => {
    const fetchDocuments = async () => {
      const userId: any = localStorage.getItem("ScalarUserId");
      // setLoading(true);
      try {
        const res = await fetchAllProjectDoc(userId);
        if (res) {
          setProjects(res);
          const projectOptions = res.map((proj: any) => ({
            value: proj.id,
            label: proj?.name,
          }));
          setAllProjectsOptions(projectOptions);
        } else {
          message.error("Failed to fetch documents");
        }
      } catch (error) {
        message.error("An error occurred while fetching documents");
      } finally {
        // setLoading(false);
      }
    };

    if (firstRender.current) {
      firstRender.current = false;

      fetchDocuments();

      const projectId = localStorage.getItem("ScalarProjectId");
      const inspectionTypeId: any = localStorage.getItem(
        "ScalarInspectionTypeId"
      );
      const viewInspectionDetails = localStorage.getItem(
        "ViewInspectionDetails"
      );
      if (projectId) {
        setSelectedProject(projectId);
        if (viewInspectionDetails && viewInspectionDetails === "true") {
        } else {
          generateInspectionTypeFields(inspectionTypeId, projectId);
          fetchDecompositionFields(inspectionTypeId);
          fetchFindingFields(inspectionTypeId);
          fetchMeasureFields(inspectionTypeId);
        }
      }
    }
  }, []);

  const getInspectionDetails = async (id: any) => {
    setLoader(true);
    const res: any = await fetchInspection(id);
    if (res) {
      setLoader(false);
      setInspectionName(
        res.name.slice(0, 1).toUpperCase() + res.name.slice(1).toLowerCase()
      );
      setInspectionType(res.reporttype.id);
      setSelectedProject(res.project.id);
      if (res?.fields) {
        const userId = localStorage.getItem("ScalarFirebaseUid");
        const parsedFields = await JSON.parse(res.fields);
        setSuggestionResponse({
          fields: parsedFields,
          inspection_type_id: res?.reporttype?.id,
          project: res?.project?.id,
          user_id: userId,
        });
        const image_field = parsedFields.find(
          (field: any) => field.type === "image_field"
        );
        if (image_field && image_field.suggested_value) {
          setFieldImageUrl(image_field.suggested_value);
        }
        setTypeFields(parsedFields);
      }
      if (res?.location) {
        setLocation(res.location);
      }
      if (res?.image) {
        setInspectionImage(res.image);
        setImageUrl(res.image);
        setDeleteImage(res.image);
      }
      if (res?.decomp_fields && res?.finding_fields && res?.measure_fields) {
        setDecompositionFields(JSON.parse(res?.decomp_fields));
        setFindingFields(JSON.parse(res?.finding_fields));
        setMeasureFields(JSON.parse(res?.measure_fields));
      }
    } else {
      setLoader(false);
      message.error(t("Something went wrong, try again later!"));
    }
  };

  const fetchIsnspectionTypes = async (firebaseUid: any) => {
    try {
      setLoader(true);
      setLoadingCard(true);
      const res: any = await getUserReportTypes(firebaseUid);
      if (res.inspectionTypes) {
        console.log('res', res)
        const inspectionTypeId: any = localStorage.getItem(
          "ScalarInspectionTypeId"
        );
        if (inspectionTypeId) {
          setInspectionType(inspectionTypeId);
        }
        setInspectionTypeList(res.inspectionTypes);
      } else {
        message.error(t("Something went wrong!"));
      }
    } catch (error) {
      message.error(t("Error fetching Inspection types."));
    } finally {
      setLoader(false);
      setLoadingCard(false);
    }
  };

  const generateInspectionTypeFields = async (
    inspectionTypeId: any,
    projectId: any
  ) => {
    try {
      setTypeFieldLoader(true);
      const res = await generateTypeFields(inspectionTypeId, projectId);
      if (res) {
        setSuggestionResponse(res);
        setTypeFields(res.fields);

        // const allTextFieldsNull = res.fields
        //   .filter((field: any) => field.type === "text")
        //   .every((field: any) => field.suggested_value === null);

        // if (allTextFieldsNull) {
        //   getTextSuggestions(res)
        // }
      }
    } catch (error) {
      message.error(t("Error fetching inspection type fields."));
    } finally {
      setTypeFieldLoader(false);
    }
  };

  const fetchDecompositionFields = async (inspectionTypeId: any) => {
    try {
      setLoadingCard(true);
      const findingTypeId: any = process.env.NEXT_DECOMP_ID;
      const res = await generateSuggestionFieldsFields(
        inspectionTypeId,
        findingTypeId
      );
      if (res) {
        // console.log('decompField', res?.suggestions)
        setDecompositionFields(res?.suggestions);
      }
    } catch (error) {
      message.error(t("Error fetching inspection type fields."));
    } finally {
      setLoadingCard(false);
    }
  };

  const fetchFindingFields = async (inspectionTypeId: any) => {
    try {
      setLoadingCard(true);
      const findingTypeId: any = process.env.NEXT_FINDING_ID;
      const res = await generateSuggestionFieldsFields(
        inspectionTypeId,
        findingTypeId
      );
      if (res) {
        // console.log('findingField', res?.suggestions)
        setFindingFields(res?.suggestions);
      }
    } catch (error) {
      message.error(t("Error fetching inspection type fields."));
    } finally {
      setLoadingCard(false);
    }
  };

  const fetchMeasureFields = async (inspectionTypeId: any) => {
    try {
      setLoadingCard(true);
      const findingTypeId: any = process.env.NEXT_MEASURE_ID;
      const res = await generateSuggestionFieldsFields(
        inspectionTypeId,
        findingTypeId
      );
      if (res) {
        // console.log('measureField', res?.suggestions)
        setMeasureFields(res?.suggestions);
      }
    } catch (error) {
      message.error(t("Error fetching inspection type fields."));
    } finally {
      setLoadingCard(false);
    }
  };

  const fetch = async () => {
    const firebaseUid: any = localStorage.getItem("ScalarFirebaseUid");

    await fetchIsnspectionTypes(firebaseUid);
    // ============================
    // const reportTypesString: any = localStorage.getItem("inspectionTypes");
    // const reportTypes = JSON.parse(reportTypesString);
    // setInspectionTypeList(reportTypes);

    // if (reportTypes?.length > 0) {
    //   setInspectionType(reportTypes[0].value);
    // }

    const viewInspectionDetails = localStorage.getItem("ViewInspectionDetails");
    if (viewInspectionDetails && viewInspectionDetails === "true") {
      setIsEdit(true);
      const inspectionId = localStorage.getItem("ScalarInspectionId");
      getInspectionDetails(inspectionId);
    } else {
      setIsEdit(false);
    }
  };

  const first = useRef(false);

  useEffect(() => {
    if (!first.current) {
      const language: any = localStorage.getItem("ScalarLanguage");
      handleChangeLanguage(language);
      let selectedLocale;
      if (language === "en") {
        selectedLocale = locales.en;
      } else {
        selectedLocale = locales.nl;
      }
      moment.locale(selectedLocale.momentLocale);
      setLocale(selectedLocale.antdLocale);

      fetch();
      first.current = true;
    }
  }, []);

  useEffect(() => {
    if (!momentDate) {
      const timestamp = Date.now();
      const dayjsObj = dayjs(timestamp);
      handleDateChange(dayjsObj);
      setMomentDate(dayjsObj);
    }
  }, [momentDate]);

  const handleDateChange = (value: any) => {
    if (value) {
      setMomentDate(value);
      const timestamp = value.valueOf(); // Convert moment to timestamp
      setDateAndTime(timestamp);
    }
  };

  const getTextSuggestions = async (suggestionObj: any) => {
    const inspectionTypeId: any = localStorage.getItem("reportTypeId");
    const inspectionId: any = localStorage.getItem("ScalarInspectionId");
    const findingTypeId: any = sessionStorage.getItem("FindingTypeId");

    try {
      setTextSuggestionLoader(true);
      const res = await fetchTextSuggestions(
        inspectionTypeId,
        inspectionId,
        findingTypeId,
        suggestionObj
      );

      if (res) {
        const textNumberFields = res?.suggestions?.fields.filter(
          (field: any) => field.type === "text" || field.type === "number"
        );
        const fieldsAfterUpdate = suggestionObj.suggestions.fields.map(
          (field: any) => {
            const textNumField = textNumberFields.find(
              (f: any) => f.id === field.id
            );
            if (textNumField) {
              return {
                ...field,
                suggested_value: textNumField.suggested_value,
              };
            } else {
              return field;
            }
          }
        );
        setSuggestionResponse(res);
        setTypeFields(fieldsAfterUpdate);
      }
    } catch (error) {
      message.error(t("Failed to get text suggestions"));
    } finally {
      setTextSuggestionLoader(false);
    }
  };

  const handleImage = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const dataUrl = reader.result as string; // Convert the result to a string
        setImageUrl(dataUrl);
      };
      reader.onerror = () => {
        message.error(t("Failed to read the file. Please try again."));
      };
      reader.readAsDataURL(file); // Use readAsDataURL for image source
    }
    setInspectionImage(file);
    if (isEdit) {
      setUpdateImage(true);
    }
  };

  const handleSetLocation = (locate: any) => {
    setLocation(locate);

    const updatedArray = typeFields.map((item: any) =>
      item.type === "location" ? { ...item, suggested_value: locate } : item
    );

    setTypeFields(updatedArray);
  };

  const handleEdit = async (e: any) => {
    e.preventDefault();
    try {
      const inspectionId: any = localStorage.getItem("ScalarInspectionId");
      const filteredTypeFields = typeFields.filter((obj: any) => obj.required);
      for (const field of filteredTypeFields) {
        if (field.suggested_value === null) {
          message.error(t("Please fill up all the required fields."));
          return; // Stop execution of handleSubmit if validation fails
        }
      }
      setLoader(true);
      const timeStamp = Timestamp.now();
      let imageUrl: any = "";
      if (updateImage) {
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: 1280,
          useWebWorker: true,
        };
        const compressedImage = await imageCompression(
          inspectionImage,
          options
        );
        imageUrl = await handleUpload(compressedImage, "inspection");
        deleteFile(deleteImage);
      }

      // let imageUrl: any = "";
      // if (inspectionImage) {
      //   const options = {
      //     maxSizeMB: 1,
      //     maxWidthOrHeight: 1280,
      //     useWebWorker: true,
      //   };
      //   const compressedImage = await imageCompression(
      //     inspectionImage,
      //     options
      //   );
      //   imageUrl = await handleUpload(compressedImage);
      // }

      const imageField = typeFields.find(
        (field: any) => field.type === "image_field"
      );

      let finalFields = [...typeFields];

      if (imageField && fieldImage) {
        const url = await handleUpload(fieldImage, "inspection");
        imageField.suggested_value = url;
        imageField.media = {
          url: url,
          resize_url: url,
          isVideo: false,
        };
        finalFields = finalFields.map((field: any) => {
          if (field.id === imageField.id) {
            return imageField;
          }
          return field;
        });
      } else if (imageField && fieldImageUrl === "") {
        imageField.suggested_value = null;
        imageField.media = null;
        finalFields = finalFields.map((field: any) => {
          if (field.id === imageField.id) {
            return imageField;
          }
          return field;
        });
      }

      // const projectRef = doc(db, "projects", selectedProject);
      // const reporttypeRef = doc(db, "report_type", inspectionType);

      const inspectionDetails: any = {
        name: inspectionName.toLowerCase(),
        ...(location && { location: location }),
        fields: JSON.stringify(typeFields),
        ...(decompositionFields && {
          decomp_fields: JSON.stringify(decompositionFields),
        }),
        ...(findingFields && { finding_fields: JSON.stringify(findingFields) }),
        ...(decompositionFields && {
          measure_fields: JSON.stringify(measureFields),
        }),
        ...(updateImage && { image: imageUrl }),
        updated_at: timeStamp,
        // project: projectRef,
        // reporttype: reporttypeRef,
      };

      const res: any = await updateInspectionDetails(
        inspectionDetails,
        inspectionId
      );
      if (res) {
        localStorage.setItem("ScalarInspectionName", inspectionName);
        message.success(t("Successfully Updated!"));
        setLoader(false);
        localStorage.setItem("ViewInspectionDetails", "false");
        router.push("/newInspection");
      } else {
        setLoader(false);
        message.error(t("Error updating inspection."));
      }
    } catch (error) {
      setLoader(false);
      message.error(t("Something went wrong, try again later!"));
    }
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    try {
      const user_id: any = localStorage.getItem("ScalarUserId");
      const projectId: any = localStorage.getItem("ScalarProjectId");
      const clientId: any = localStorage.getItem("client");
      const filteredTypeFields = typeFields.filter((obj: any) => obj.required);
      for (const field of filteredTypeFields) {
        if (field.suggested_value === null) {
          message.error(t("Please fill up all the required fields."));
          return; // Stop execution of handleSubmit if validation fails
        }
      }

      const updatedFields = typeFields.map((field: any) => {
        if (!field.required && !field.suggested_value) {
          return {
            ...field,
            suggested_value: "",
          };
        } else return field;
      });

      setLoader(true);
      const timeStamp = Timestamp.now();
      let imageUrl: any = "";
      if (inspectionImage) {
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: 1280,
          useWebWorker: true,
        };
        const compressedImage = await imageCompression(
          inspectionImage,
          options
        );
        imageUrl = await handleUpload(compressedImage, "inspection");
      }

      const imageField = updatedFields.find(
        (field: any) => field.type === "image_field"
      );

      let finalFields = [...updatedFields];

      if (imageField && fieldImage) {
        const url = await handleUpload(fieldImage, "inspection");
        imageField.suggested_value = url;
        imageField.media = {
          url: url,
          resize_url: url,
          isVideo: false,
        };
        finalFields = finalFields.map((field: any) => {
          if (field.id === imageField.id) {
            return imageField;
          }
          return field;
        });
      }

      const inspectionDetails: any = {
        name: inspectionName.toLowerCase(),
        fields: JSON.stringify(finalFields),
        ...(decompositionFields
          ? { decomp_fields: JSON.stringify(decompositionFields) }
          : { decomp_fields: {} }),
        ...(findingFields
          ? { finding_fields: JSON.stringify(findingFields) }
          : { finding_fields: {} }),
        ...(decompositionFields
          ? { measure_fields: JSON.stringify(measureFields) }
          : { measure_fields: {} }),
        isCompleted: false,
        ...(location && { location: location }),
        image: imageUrl,
        client: clientId,
        created_at: timeStamp,
        updated_at: timeStamp,
      };

      // console.log('inspectionDetails', inspectionDetails)
      // setLoader(false);
      // return

      localStorage.setItem("ScalarProjectId", projectId);

      const res: any = await addInspectionDetail(
        inspectionDetails,
        inspectionType,
        user_id,
        selectedProject
      );
      // return
      if (res?.type) {
        localStorage.setItem("ScalarInspectionName", inspectionName);
        const inspectionId = await res.data.id;
        const templateField = finalFields.find(
          (field: any) => field.type === "template"
        );
        if (templateField) {
          if (
            templateField.suggested_value !== null &&
            templateField.suggested_value !== "0"
          ) {
            const allFindings = await fetchAllFindings(
              user_id,
              templateField.suggested_value
            );
            const temp_inspection = await fetchInspection(
              templateField.suggested_value
            );
            let findingOrder: any = [];
            if (temp_inspection) {
              findingOrder = temp_inspection?.finding_order.map(
                (order: any) => order?.id
              );
            }
            if (allFindings) {
              // Helper function to recursively flatten all descendants of a finding
              const flattenFindings = (finding: Finding): Finding[] => {
                const children = finding.children || [];
                return [finding, ...children.flatMap(flattenFindings)];
              };

              // Helper function to build hierarchical structure for findings
              const buildHierarchy = (
                findings: Finding[],
                parentId: string | null
              ): Finding[] => {
                return findings
                  .filter((finding) => finding.parent_finding_id === parentId)
                  .map((finding) => ({
                    ...finding,
                    children: buildHierarchy(findings, finding.id),
                  }));
              };

              // Build the initial hierarchical structure
              const hierarchy = buildHierarchy(allFindings, null);

              let sortedHierarchy;

              if (findingOrder && findingOrder.length > 0) {
                // If finding order exists, sort according to it
                sortedHierarchy = [...hierarchy].sort((a, b) => {
                  const indexA = findingOrder.indexOf(a.id);
                  const indexB = findingOrder.indexOf(b.id);

                  if (indexA !== -1 && indexB !== -1) {
                    return indexA - indexB;
                  }
                  if (indexA !== -1) return -1;
                  if (indexB !== -1) return 1;
                  return 0;
                });
              } else {
                // If no finding order, use the original hierarchy
                sortedHierarchy = hierarchy;
              }

              // Map the hierarchy into the grouped structure
              const grouped = sortedHierarchy.map((parent) => {
                const allDescendants = flattenFindings(parent).slice(1);

                let findings = allDescendants.filter(
                  (item) => item.parent_finding_id === parent.id
                );

                // Only sort findings if finding order exists
                if (findingOrder && findingOrder.length > 0) {
                  findings = findings.sort((a, b) => {
                    const indexA = findingOrder.indexOf(a.id);
                    const indexB = findingOrder.indexOf(b.id);

                    if (indexA !== -1 && indexB !== -1) return indexA - indexB;
                    if (indexA !== -1) return -1;
                    if (indexB !== -1) return 1;
                    return 0;
                  });
                }

                return {
                  id: parent.id,
                  decompositionItem: parent,
                  findings: findings.map((finding) => {
                    let measures = allDescendants.filter(
                      (item) => item.parent_finding_id === finding.id
                    );

                    // Only sort measures if finding order exists
                    if (findingOrder && findingOrder.length > 0) {
                      measures = measures.sort((a, b) => {
                        const indexA = findingOrder.indexOf(a.id);
                        const indexB = findingOrder.indexOf(b.id);

                        if (indexA !== -1 && indexB !== -1)
                          return indexA - indexB;
                        if (indexA !== -1) return -1;
                        if (indexB !== -1) return 1;
                        return 0;
                      });
                    }

                    return {
                      finding: finding,
                      measure: measures,
                    };
                  }),
                };
              });

              await addFindingsForInspection(grouped, inspectionId);
            }
          }
        }
        localStorage.setItem("ScalarInspectionId", inspectionId);
        message.success(t("Successfully saved!"));
        router.push("/newInspection");
      } else {
        setLoader(false);
        message.error(t("Something went wrong, try again later!"));
      }
    } catch (error) {
      setLoader(false);
      message.error(t("Something went wrong, try again later!"));
    } finally {
      setLoader(false);
    }
  };

  return (
    <>
      {loader && <Loader />}
      <div className="flex h-screen">
        <Sidebar />
        <div
          className={`${
            isCollapsed ? "w-[calc(100vw-60px)]" : "w-[calc(100vw-16%)]"
          }`}
        >
          <Navbar
            search={false}
            searchInspection={searchInspection}
            setSearch={setSearchInspection}
          />
          <div
            className={`h-[calc(100%-60px)] text-black ${poppins.className} overflow-auto scrollbar`}
          >
            <div className="sticky top-0 bg-white pb-4 z-10">
              <div className="w-full flex justify-between items-center px-10 pt-6 mb-1 sticky top-0 bg-white">
                <h1 className="flex gap-1 text-[24px] leading-[24px] font-[500]">
                  <p
                    className="hover:underline cursor-pointer transition-all"
                    onClick={() => {
                      console.log("prevPath", prevPath);
                      prevPath === "allInspections"
                        ? router.push("/inspections")
                        : prevPath === "Project"
                        ? router.push("/project")
                        : prevPath === "newInspection"
                        ? router.push("/newInspection")
                        : router.push("/home");
                    }}
                  >
                    {prevPath === "allInspections"
                      ? t("All Inspections")
                      : prevPath === "Project"
                      ? searchParams.get("pname")
                      : prevPath === "newInspection"
                      ? searchParams.get("inspection")
                      : t("Home")}
                  </p>{" "}
                  <RightOutlined className="text-[18px]" />
                  {" "}
                  {t("Inspection Details")}
                </h1>
                <div className="flex gap-4">
                  <Button
                    onClick={() => router.back()}
                    type="primary"
                    className={`${poppins.className} custom-button  w-[100px] rounded-xl text-[14px] border-opacity-30 bg-[#2F80ED] text-white h-[44px]`}
                  >
                    {t("Back")}
                  </Button>
                  <Button
                    type="primary"
                    className={`w-[100px] h-[44px] custom-button-disable text-[14px] leading-[14px] font-[500] ${
                      inspectionType === "" || inspectionName === ""
                        ? "bg-[#2f81ed6f]"
                        : "bg-[#2F80ED]"
                    } text-white flex items-center gap-2 p-3 rounded-xl`}
                    onClick={isEdit ? handleEdit : handleSubmit}
                    disabled={
                      inspectionType === "" ||
                      inspectionName === "" ||
                      inspectionImage === null ||
                      selectedProject === null
                    }
                  >
                    {isEdit ? <>{t("Update")}</> : <>{t("Save")}</>}
                  </Button>
                </div>
              </div>
            </div>

            <div
              className={`w-full px-10 md:mt-6 flex gap-4`}
            >
              {/* Labels */}
              <div className="w-[15.5%] flex flex-col gap-8 items-start text-center text-[14px]">
                <label className="font-semibold mt-4">
                  {t("Project")}
                  <Tooltip
                    placement="right"
                    title="Required"
                    className="cursor-pointer"
                  >
                    <sup className="text-red-500">*</sup>
                  </Tooltip>
                </label>

                <label className="font-semibold mt-4">
                  {t("Inspection Type")}
                  <Tooltip
                    placement="right"
                    title="Required"
                    className="cursor-pointer"
                  >
                    <sup className="text-red-500">*</sup>
                  </Tooltip>
                </label>

                <label className="font-semibold mt-2">
                  {t("Name")}
                  <Tooltip
                    placement="right"
                    title="Required"
                    className="cursor-pointer"
                  >
                    <sup className="text-red-500">*</sup>
                  </Tooltip>
                </label>

                <label className="font-semibold mt-2">
                  {t("Image")}
                  <Tooltip
                    placement="right"
                    title="Required"
                    className="cursor-pointer"
                  >
                    <sup className="text-red-500">*</sup>
                  </Tooltip>
                </label>
              </div>

              {/* Vertical Divider */}
              <div className="border-r-2 border-gray-300 ml-4" />

              {/* Inputs */}
              <div className="w-[84.5%] flex flex-col">
                {/* Project */}
                <div
                  className="border-b border-gray-200 py-4"
                  style={{ borderBottomWidth: "2px" }}
                >
                  <Select
                    className="w-full"
                    value={loader ? null : selectedProject}
                    onChange={(value) => {
                      setSelectedProject(value);
                      const newInspectionType = Projects?.find(
                        (proj: any) => proj.id === value
                      )?.inspection_type?.id;
                      setInspectionType(newInspectionType);
                      generateInspectionTypeFields(newInspectionType, value);
                      fetchDecompositionFields(newInspectionType);
                      fetchFindingFields(newInspectionType);
                      fetchMeasureFields(newInspectionType);
                    }}
                    disabled={isEdit}
                    placeholder={t("Select Project")}
                    options={allProjectsOptions}
                    style={{ width: "100%", height: "28px" }}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label as string)
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </div>

                {/* Inspection Type */}
                <div
                  className="border-b border-gray-200 py-4"
                  style={{ borderBottomWidth: "2px" }}
                >
                  <Button
                    shape="round"
                    className="bg-blue-500 text-white border h-[25px] text-[12px] p-0"
                    style={{
                      backgroundColor: "rgb(59 130 246)",
                      color: "white",
                    }}
                  >
                    {inspectionTypeList
                      ? inspectionTypeList.find(
                          (opt: any) => opt.value === inspectionType
                        )?.label || inspectionType
                      : t("Inspection Type")}
                  </Button>
                </div>

                {/* Name */}
                <div
                  className="border-b border-gray-200 py-4"
                  style={{ borderBottomWidth: "2px" }}
                >
                  <Input
                    type="text"
                    placeholder={t("Enter name")}
                    value={inspectionName}
                    onChange={(e) => setInspectionName(e.target.value)}
                    className="border rounded-[10px] text-[12px]"
                    // style={{ border: "none" }}
                  />
                </div>

                {/* Image Upload */}
                <div
                  className="flex items-center gap-2 border-b border-gray-200 py-4"
                  style={{ borderBottomWidth: "2px" }}
                >
                  <Button
                    shape="round"
                    onClick={() => inputRef.current.click()}
                    type="primary"
                    className="custom-button h-[30px] text-[12px] px-4 border-opacity-30 bg-[#2F80ED] text-white"
                  >
                    <Image
                      width={15}
                      height={15}
                      alt="logo"
                      src="/images/newInspection/image.png"
                      className="w-[15px] h-[15px]"
                    />
                    <p className="text-white text-[12px] font-[400]">
                      {inspectionImage !== null || imageUrl !== ""
                        ? t("Replace Image")
                        : t("Select Image")}
                    </p>
                    <input
                      type="file"
                      ref={inputRef}
                      onChange={handleImage}
                      accept="image/*"
                      className="hidden"
                    />
                  </Button>

                  {(inspectionImage !== null || imageUrl !== "") && (
                    <div
                      onClick={() => setShowMedia(true)}
                      className="flex items-center gap-1 cursor-pointer"
                    >
                      <img
                        width={30}
                        height={30}
                        alt=""
                        src={imageUrl}
                        className="w-[30px] h-[30px] rounded-md object-contain border"
                      />
                    </div>
                  )}

                  <Media
                    imageUrl={imageUrl}
                    showMedia={showMedia}
                    setShowMedia={setShowMedia}
                  />
                </div>
              </div>
            </div>
            {/* 
            </div> */}

            <div className="w-full text-[14px]">
              {typeFieldLoader ? (
                // <LoadingCards />
                <div className="space-y-4 animate-pulse mb-4">
                  {/* Each field row */}
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="flex items-center gap-0 mt-6">
                      <div className="flex-1 h-[50px] bg-gray-100 rounded-xl"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <>
                  {selectedProject ? (
                    <InspectionTypeFields
                      editableFindings={typeFields}
                      setEditableFindings={setTypeFields}
                      textareaRef={textareaRef}
                      isAddingMeasure={false}
                      isMeasureUpdate={false}
                      setLocationModalOpen={setLocationModalOpen}
                      isLocationSelected={location}
                      isEdit={isEdit}
                      t={t}
                      {...{
                        fieldImage,
                        setFieldImage,
                        fieldImageUrl,
                        setFieldImageUrl,
                        getTextSuggestions,
                        suggestionResponse,
                        textSuggestionLoader,
                      }}
                    />
                  ) : (
                    <div className="my-4 px-4">
                      Please Select a project to generate fields.
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      <LocationModal
        locationModalOpen={locationModalOpen}
        setLocationModalOpen={setLocationModalOpen}
        location={location}
        setLocation={(lat: number, lon: number) =>
          handleSetLocation({ lat, lon })
        }
        googleMapsApiKey={process.env.NEXT_GOOGLE_PLACE_API}
        isEdit={isEdit}
      />
    </>
  );
};

export default InspectionMetaData;
