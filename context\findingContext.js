"use client";
import React, { createContext, useContext, useState } from "react";

const FindingContext = createContext();

export const FindingContextProvider = ({ children }) => {
  const [isUpdate, setIsUpdate] = useState(false);
  const [UpdateInspection, setUpdateInspection] = useState(null);
  const [isLoadingCard, setIsLoadingCard] = useState(false);
  const [newAddedId, setNewAddedId] = useState("");
  const [imgSource, setImgSource] = useState(null);
  const [wholeGroup, setWholeGroup] = useState(null);

  return (
    <FindingContext.Provider
      value={{
        isUpdate,
        setIsUpdate,
        UpdateInspection,
        setUpdateInspection,
        isLoadingCard,
        setIsLoadingCard,
        newAddedId,
        setNewAddedId,
        imgSource,
        setImgSource,
        wholeGroup,
        setWholeGroup,
      }}
    >
      {children}
    </FindingContext.Provider>
  );
};

export const useFindingContext = () => {
  return useContext(FindingContext);
};
