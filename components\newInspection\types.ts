export interface Finding {
  id: string;
  title_fields: string[];
  fields: string;
  media: Array<{
    isVideo: boolean;
    url: string;
  }>;
  parent_finding_id: string | null;
  finding_type: {
    id: string;
  };
  approved: boolean;
  field_values: any;
}

export interface DecompositionGroup {
  id: string;
  title: string;
  decomposition: any,
  findings: Finding[];
  isApproved: boolean;
}

export interface OrderItem {
  id: string;
  type: 'decomposition' | 'finding' | 'measure';
  parentId: string | null;
} 