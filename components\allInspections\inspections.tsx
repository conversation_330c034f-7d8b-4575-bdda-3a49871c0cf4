"use client";
import React, { useEffect, useState, useRef, useCallback } from "react";
import Navbar from "@/components/Navbar/navbar";
import Sidebar from "@/components/sidebar/sidebar";
import { <PERSON><PERSON>, <PERSON><PERSON>s, Open_Sans } from "next/font/google";
import Image from "next/image";
import { Button, Input, Modal, Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { useLoaderContext } from "@/context/LoaderContext";
import Loader from "@/components/loader/loader";
import { message } from "antd";
import {
  fetchAllInspections,
  deleteInspection,
  fetchInspectionsByIds,
} from "@/src/services/home.api";
import { inspectionTypeList } from "@/src/libs/constants";
import DeleteModal from "@/components/deleteModal/deleteModal";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import InspectionTable from "./inspectionsTable";
import InspectionMap from "./inspectionsmapView";
import { getUserReportTypes } from "@/src/services/auth.api";
import ActionButtons from "./actionButtons";
import { useSidebarContext } from "@/context/sidebarContext";
import { fetchAllDocuments, getProjects } from "@/src/services/project.api";
import InfiniteScroll from "react-infinite-scroll-component";
import {
  getInspections,
  getProjectNames,
} from "@/src/services/allInspections.api";
import debounce from "debounce";
import ProjectSelectionModal from "./projectSelectionModal";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Home = () => {
  const [Inspections, setInspections] = useState<any>([]);
  const [searchedInspections, setSearchedInspections] = useState<any>([]);
  const [searchInspection, setSearchInspection] = useState<any>("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isActionMenuVisible, setIsActionMenuVisible] = useState(false);
  const [isProjectSelectionModalOpen, setIsProjectSelectionModalOpen] =
    useState(false);
  const [deleteId, setDeleteId] = useState("");
  const [projectId, setProjectId] = useState("");
  const [refresh, setRefresh] = useState(false);
  const [inspectionTypeList, setInspectionTypeList] = useState<any>(null);
  const [isMapView, setIsMapView] = useState(false);
  const [selectedInspections, setSelectedInspections] = useState<any>([]);
  const [selectedInspectionObj, setSelectedInspectionObj] = useState<any>([]);
  const menuRef = useRef<HTMLDivElement>(null);

  const [projects, setProjects] = useState<any>([]);
  const [search, setSearch] = useState<any>("");
  const [hasMany, setHasMany] = useState(false);
  const [lastDoc, setLastDoc] = useState<string | null>(null);
  const [searchLoader, setSearchLoader] = useState(false);
  const [projectNames, setProjectNames] = useState<any>({});

  const router = useRouter();
  const { t } = useTranslation();

  const { loader, setLoader } = useLoaderContext();
  const { isCollapsed } = useSidebarContext();

  // const fetchInspections = async (userId: any) => {
  //   // const projects: any = localStorage.getItem("projects")
  //   // const parsedProjects: any = JSON.parse(projects)

  //   const projectsArray: any = await fetchAllDocuments(userId);
  //   setAllProjects(projectsArray)
  //   const projectIds = projectsArray.map((project: any) => project.id);
  //   const inspectionsArray: any = await fetchAllInspections(userId, projectIds);
  //   if (inspectionsArray) {
  //     const inspections: any = inspectionsArray.sort(
  //       (a: any, b: any) => b.created_at - a.created_at
  //     );
  //     setInspections(inspections);
  //     setSearchedInspections(inspections);
  //   } else {
  //     message.error(t("Something went wrong!"));
  //   }
  // };

  const fetchInspections = async () => {
    try {
      const params = {
        search,
        isForFirst: true,
        lastDocument: lastDoc,
        limitValue: 20,
      };
      const inspectionsArray: any = await getInspections(params);
      if (inspectionsArray) {
        console.log("inspectionsArray", inspectionsArray);
        const projectIds = inspectionsArray.list.map(
          (insp: any) => insp.project.id
        );
        const uniqueIds: any = Array.from(new Set(projectIds));
        const res = await getProjectNames(uniqueIds);
        console.log("res", res);
        if (res) {
          setProjectNames(res);
        }
        setInspections(inspectionsArray.list);
        setHasMany(inspectionsArray.hasMore);
        setLastDoc(inspectionsArray.lastDocument);
      } else {
        message.error(t("Something went wrong!"));
      }
    } catch (error) {
      message.error(t("Something went wrong!"));
    }
  };
  console.log("projectNames", projectNames);

  const fetchMoreInspections = async () => {
    try {
      const params = {
        search: search.toLocaleLowerCase(),
        isForFirst: false,
        lastDocument: lastDoc,
        limitValue: 20,
      };
      const inspectionsArray: any = await getInspections(params);
      if (inspectionsArray) {
        const projectIds = inspectionsArray.list.map(
          (insp: any) => insp.project.id
        );
        const uniqueIds: any = Array.from(new Set(projectIds));
        const res = await getProjectNames(uniqueIds);
        if (res) {
          setProjectNames((prev: any) => ({
            ...prev,
            ...res,
          }));
        }
        setInspections((prev: any) => [...prev, ...inspectionsArray.list]);
        setHasMany(inspectionsArray.hasMore);
        setLastDoc(inspectionsArray.lastDocument);
      } else {
        message.error(t("Something went wrong!"));
      }
    } catch (error) {
      message.error(t("Something went wrong!"));
    }
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    // if (value.trim() === "") return;
    debouncedSearchInspections(value.trim());
  };

  const debouncedSearchInspections = useCallback(
    debounce(async (value: string) => {
      try {
        setSearchLoader(true);
        const params = {
          search: value.toLocaleLowerCase(),
          isForFirst: true,
          lastDocument: lastDoc,
          limitValue: 20,
        };
        const inspectionsArray: any = await getInspections(params);
        if (inspectionsArray) {
          const projectIds = inspectionsArray.list.map(
            (insp: any) => insp.project.id
          );
          const uniqueIds: any = Array.from(new Set(projectIds));
          const res = await getProjectNames(uniqueIds);
          if (res) {
            setProjectNames(res);
          }
          setInspections(inspectionsArray.list);
          setHasMany(inspectionsArray.hasMore);
          setLastDoc(inspectionsArray.lastDocument);
        } else {
          message.error(t("Something went wrong!"));
        }
      } catch (error) {
        message.error(t("Something went wrong!"));
      } finally {
        setSearchLoader(false);
      }
    }, 500), // 1000ms delay
    []
  );

  const fetchIsnspectionTypes = async (userId: any) => {
    try {
      const res: any = await getUserReportTypes(userId);
      if (res.inspectionTypes) {
        setInspectionTypeList(res.inspectionTypes);
      } else {
        message.error(t("Something went wrong!"));
      }
    } catch (error) {
      message.error(t("Error fetching Inspection types."));
    }
  };

  const fetchInspectionAndInspectionTypes = async (
    userId: string,
    firebaseUid: string
  ) => {
    try {
      setLoader(true);
      await fetchIsnspectionTypes(firebaseUid);
      await fetchInspections();
    } catch (error) {
      message.error(t("Something went wrong!"));
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    const laguage: any = localStorage.getItem("ScalarLanguage");
    handleChangeLanguage(laguage);
    localStorage.removeItem("ViewInspectionDetails");
    const firebaseUid: any = localStorage.getItem("ScalarFirebaseUid");
    const userId: any = localStorage.getItem("ScalarUserId");
    localStorage.removeItem("ScalarProjectId");

    fetchInspectionAndInspectionTypes(userId, firebaseUid);
  }, [refresh]);

  useEffect(() => {
    const searchArray = Inspections.filter((item: any) => {
      return item.name
        .toLowerCase()
        .includes(searchInspection.trim().toLowerCase());
    });
    setSearchedInspections(searchArray);
  }, [searchInspection]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsActionMenuVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleDelete = async (e: any) => {
    e.preventDefault();

    const inspectionDetails: any = {
      is_deleted: true,
    };
    setLoader(true);
    const res: any = await deleteInspection(
      inspectionDetails,
      deleteId,
      projectId
    );
    if (res) {
      message.success(t("Successfully deleted!"));
      setLoader(false);
      setIsModalOpen(false);
      setRefresh(!refresh);
    } else {
      setLoader(false);
      setIsModalOpen(false);
      setRefresh(!refresh);
      message.error(t("Something went wrong, try again later!"));
    }
  };

  // const handleInspectionClick = (inspection: any) => {
  //   localStorage.setItem("ScalarInspectionId", inspection.id);
  //   const inspectionName =
  //     inspection.name.slice(0, 1).toUpperCase() +
  //     inspection.name.slice(1).toLowerCase();
  //   localStorage.setItem("ScalarInspectionName", inspectionName);
  //   router.push("/newInspection");
  // };
  const handleInspectionClick = (inspection: any) => {
    console.log("inspection", inspection);

    // Store inspection ID
    localStorage.setItem("ScalarInspectionId", inspection.id);

    const projectRef = inspection.projectList?.[0]; // Assuming first project
    const projectId = projectRef?.id;

    // Get project name from lookup table (projectNames)
    const projectNameRaw = projectNames?.[projectId] || "";
    const projectName =
      projectNameRaw.slice(0, 1).toUpperCase() +
      projectNameRaw.slice(1).toLowerCase();

    localStorage.setItem("ScalarProjectId", projectId);
    localStorage.setItem("ScalarProjectName", projectName);

    // Format and store inspection name
    const inspectionName =
      inspection.name.slice(0, 1).toUpperCase() +
      inspection.name.slice(1).toLowerCase();
    localStorage.setItem("ScalarInspectionName", inspectionName);

    // Navigate to the inspection page
    router.push("/newInspection");
  };

  useEffect(() => {
    console.log("hello");
  }, []);

  return (
    <>
      {loader && <Loader />}
      <div className="flex h-screen">
        <Sidebar />
        <div
          className={`${
            isCollapsed ? "w-[calc(100vw-60px)]" : "w-[calc(100vw-16%)]"
          }`}
        >
          <Navbar
            search={false}
            searchInspection={searchInspection}
            setSearch={setSearchInspection}
          />
          <div
            className={`h-[calc(100%-60px)] text-black ${poppins.className} pb-10 relative`}
          >
            <div className="w-full bg-white flex justify-between items-center px-10 pt-6 pb-4 sticky top-0 z-10">
              <h1 className="pt-3 text-[24px] leading-[24px] font-[500]">
                {t("Inspections")}
              </h1>
            </div>

            <div className="bg-white flex justify-between px-10 items-center pb-6 sticky top-16 z-10">
              <Input
                className="w-[40%] px-3 h-[35px] bg-[#2F80ED0D] border-none"
                placeholder={t("Search")}
                value={search}
                suffix={
                  <Image
                    width={12}
                    height={12}
                    className={`${
                      search !== ""
                        ? "w-[30px] relative left-2 cursor-pointer"
                        : "w-[12px]"
                    } ${search !== "" ? "h-[30px]" : "h-[12px]"}`}
                    src={
                      search.length !== 0
                        ? "/images/cancel.svg"
                        : "/images/Icon.png"
                    }
                    alt="icon"
                    onClick={
                      search !== ""
                        ? () => {
                            setSearch("");
                            debouncedSearchInspections("");
                          }
                        : () => {}
                    }
                  />
                }
                onChange={(e) => handleSearch(e.target.value)}
              />

              {/* Buttons */}
              <div className="flex gap-2">
                <div className="">
                  <Button
                    onClick={() =>
                    {
                      router.push(`/newInspection/inspectionMetaData?prev=allInspections`)
                    }
                    }
                    // onClick={() => setIsProjectSelectionModalOpen(true)}
                    type="primary"
                    className={`${poppins.className} custom-button px-4 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px] `}
                  >
                    <Image
                      width={20}
                      height={20}
                      alt="logo"
                      src={"/images/home/<USER>"}
                      className="w-[20px] h-[20px]"
                    />
                    <p className={`text-white text-[14px] font-[400]`}>
                      {t("New Inspection")}
                    </p>
                  </Button>
                  <ProjectSelectionModal
                    visible={isProjectSelectionModalOpen}
                    onCancel={() => setIsProjectSelectionModalOpen(false)}
                    onSelectProject={(project: any) => {
                      console.log("Selected project:", project);
                      localStorage.setItem("ScalarProjectId", project.id);
                      localStorage.setItem("ScalarProjectName", project.name);
                      localStorage.setItem(
                        "ScalarInspectionTypeId",
                        project.inspection_type.id
                      );
                      setIsProjectSelectionModalOpen(false);
                      router.push("/newInspection/inspectionMetaData");
                    }}
                    // projectNames={["Project Alpha", "Project Beta", "Project Gamma"]}
                    // projectNames={Object.entries(projectNames || {}).map(
                    //   ([id, name]) => ({
                    //     id,
                    //     name,
                    //   })
                    // )}
                  />
                </div>
                <div className="relative">
                  <Button
                    onClick={() => setIsActionMenuVisible(!isActionMenuVisible)}
                    type="primary"
                    className={`${poppins.className} w-[100px] custom-button-disable px-4 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px]`}
                    disabled={Inspections.length === 0}
                  >
                    {t("Actions")}
                  </Button>
                  {isActionMenuVisible && (
                    <ActionButtons
                      setIsActionMenuVisible={setIsActionMenuVisible}
                      selectedInspections={selectedInspections}
                      selectedInspectionObj={selectedInspectionObj}
                      setSearchedInspections={setSearchedInspections}
                      searchedInspections={Inspections}
                      setRefresh={setRefresh}
                      t={t}
                    />
                  )}
                </div>
              </div>
            </div>

            {Inspections.length === 0 ? (
              <div className="w-full h-[calc(100vh-204px)] mt-26 flex justify-center items-center">
                <div className="w-[352px] text-left">
                  <div className="w-full flex justify-center items-center">
                    <Image
                      src={"/images/home/<USER>"}
                      width={352}
                      height={337}
                      alt={t(
                        "Start a new inspection by clicking New Inspection"
                      )}
                      className="w-[35vh]"
                    />
                  </div>
                  <p className="text-[14px] leading-[20.8px] text-center font-[400] text-[#626d7d] mt-4">
                    {t("Start a new inspection by clicking New Inspection")}
                  </p>
                </div>
              </div>
            ) : (
              <div
                id="pojectInspScroll"
                className="px-10 h-[calc(100vh-204px)] overflow-y-auto scrollbar"
              >
                {!isMapView ? (
                  <>
                    {!searchLoader && (
                      <InfiniteScroll
                        dataLength={Inspections.length}
                        next={fetchMoreInspections}
                        hasMore={hasMany}
                        loader={
                          <div className="w-full flex justify-center items-center gap-2 mb-6">
                            <Spin
                              size="default"
                              indicator={<LoadingOutlined spin />}
                            />
                            {t("Loading")}...
                          </div>
                        }
                        scrollThreshold={0.8}
                        scrollableTarget="pojectInspScroll"
                      >
                        <InspectionTable
                          {...{
                            Inspections,
                            inspectionTypeList,
                            projectNames,
                            setDeleteId,
                            setProjectId,
                            setIsModalOpen,
                            handleInspectionClick,
                            selectedInspections,
                            setSelectedInspections,
                            selectedInspectionObj,
                            setSelectedInspectionObj,
                          }}
                        />
                      </InfiniteScroll>
                    )}
                    {searchLoader && (
                      <div className="w-full h-full flex justify-center items-center gap-2 mb-4">
                        <Spin
                          size="large"
                          indicator={<LoadingOutlined spin />}
                        />
                        {t("Searching")}...
                      </div>
                    )}
                  </>
                ) : (
                  <div className="w-full h-[65vh] flex justify-center items-center bg-gray-200 rounded-[12px]">
                    <InspectionMap
                      inspections={Inspections}
                      googleMapsApiKey={process.env.NEXT_GOOGLE_PLACE_API}
                      handleInspectionClick={handleInspectionClick}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        {isModalOpen && (
          <DeleteModal
            handleDelete={handleDelete}
            setDeleteId={setDeleteId}
            setIsModalOpen={setIsModalOpen}
          />
        )}
      </div>
    </>
  );
};

export default Home;
