'use client';

import React, { useState } from 'react';
import { Poppins } from 'next/font/google';
import { useTranslation } from 'react-i18next';
import { Button, Modal as AntdModal, message } from 'antd';
import { deleteProject } from '@/src/services/project.api';

const poppins = Poppins({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
});

interface DeleteModalProps {
  selectedProjects: string[];
  setRefresh: (cb: (prev: any) => any) => void;
  setIsModalOpen: (value: boolean) => void;
  isModalOpen: boolean;
  setMenu: (val: boolean) => void;
}

const DeleteModal: React.FC<DeleteModalProps> = ({
  selectedProjects,
  setRefresh,
  setIsModalOpen,
  isModalOpen,
  setMenu,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    try {
      setLoading(true);
      const res: any = await deleteProject(selectedProjects);
      if (res) {
        message.success(t('Successfully deleted!'));
      } else {
        message.error(t('Something went wrong, try again later!'));
      }
    } catch (err) {
      message.error(t('Something went wrong, try again later!'));
    } finally {
      setLoading(false);
      setIsModalOpen(false);
      setMenu(false);
      setRefresh((prev: any) => !prev);
    }
  };

  return (
    <AntdModal
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      centered
      className={`${poppins.className} custom-delete-modal`}
      width={350}
    >
      <div className="relative">

        <div className="mt-6">
          <h1 className="text-left text-[25px] leading-[52.08px] font-[500]">
            {t('Delete Inspection!')}
          </h1>
          <p className="text-[18px] text-left mt-1">
            {t('Are you sure you want to')} <br />
            {t('delete Inspection?')}
          </p>

          <div className="text-center flex justify-between gap-4 mt-10">
            <Button
              type="default"
              className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] bg-[#ff910020] hover:bg-[#ff910015]"
              onClick={() => setIsModalOpen(false)}
            >
              {t('Cancel')}
            </Button>

            <Button
              type="primary"
              className="w-[50%] h-[45px] text-[14px] border-none"
              style={{
                backgroundColor: '#FF9200',
                color: 'white',
              }}
              loading={loading}
              onClick={handleDelete}
            >
              {t('Delete')}
            </Button>
          </div>
        </div>
      </div>
    </AntdModal>
  );
};

export default DeleteModal;
