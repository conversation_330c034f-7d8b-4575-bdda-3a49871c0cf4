import React, { useState, useEffect } from "react";
import Image from "next/image";
import { Tooltip, Spin } from "antd";
import { LoadingOutlined } from '@ant-design/icons';

const Media = ({
  isUpdate,
  UpdateInspection,
  handleViewUpdateInspectionMedia,
  media,
  handleViewMedia,
  setUrlsToDelete,
  urlsToDelete,
  setUpdateInspection,
  setMedia,
  setImagesArray,
  setVideosArray,
  imagesArray,
  videosArray,
  isEditMode,
}: any) => {
  // Track loading state for each media item
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  // Helper function to generate a unique key for each media item
  const getMediaKey = (url: string, index: number) => `${url}-${index}`;

  // Set all media to loading state initially
  useEffect(() => {
    const newLoadingStates: Record<string, boolean> = {};
    
    // Set loading states for existing media
    if (isUpdate && UpdateInspection?.media) {
      UpdateInspection.media.forEach((item: any, index: number) => {
        newLoadingStates[getMediaKey(item.url, index)] = true;
      });
    }
    
    // Set loading states for new media
    if (media) {
      media.forEach((item: any, index: number) => {
        newLoadingStates[getMediaKey(item.src, index)] = true;
      });
    }
    
    setLoadingStates(newLoadingStates);
  }, [isUpdate, UpdateInspection?.media, media]);

  // Handler for when media loads
  const handleMediaLoaded = (key: string) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: false
    }));
  };

  // Handler for media load errors
  const handleMediaError = (key: string) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: false
    }));
  };

  return (
    <>
      {isUpdate &&
        UpdateInspection?.media.map((item: any, index: number) => {
          const mediaKey = getMediaKey(item.url, index);
          const isLoading = loadingStates[mediaKey];

          return (
            <div
              className="w-[150px] h-[150px] rounded-[15px] flex justify-center items-center cursor-pointer relative"
              key={index}
              onClick={() =>
                handleViewUpdateInspectionMedia(item.url, item.isVideo)
              }
            >
              {isEditMode && (
                <Tooltip title="Delete" placement="bottom">
                  <Image
                    src="/images/home/<USER>"
                    width={24}
                    height={24}
                    alt="image"
                    className="w-[24px] h-[24px] object-cover absolute top-2 right-2 z-10 bg-white rounded-lg"
                    onClick={(e) => {
                      e.stopPropagation();

                      const mediaIndex = UpdateInspection.media.findIndex(
                        (mediaItem: any) => mediaItem.url === item.url
                      );

                      // const resizedMediaUrl = UpdateInspection.resize_media?.[mediaIndex]?.url;

                      setUrlsToDelete([
                        ...urlsToDelete,
                        {
                          isVideo: item.isVideo,
                          mediaIndex: mediaIndex,
                          url: item.url,
                          resize_url: item.resize_url,
                        },
                      ]);

                      const updatedMedia = UpdateInspection.media.filter(
                        (mediaItem: any, index: number) => index !== mediaIndex
                      );

                      // const updatedResizedMedia = UpdateInspection.resize_media?.filter(
                      //   (_: any, index: number) => index !== mediaIndex
                      // );

                      setUpdateInspection({
                        ...UpdateInspection,
                        media: updatedMedia,
                        // resize_media: updatedResizedMedia
                      });
                    }}
                  />
                </Tooltip>
              )}
              
              {/* Show loader while image is loading */}
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50 rounded-[15px] z-10">
                  <Spin indicator={<LoadingOutlined spin />} />
                </div>
              )}
              
              {!item.isVideo ? (
                <img
                  src={item.url}
                  width={150}
                  height={150}
                  alt="image"
                  className={`w-full h-full rounded-[15px] border object-cover ${isLoading ? 'opacity-50' : 'opacity-100'}`}
                  onLoad={() => handleMediaLoaded(mediaKey)}
                  onError={(e) => {
                    handleMediaError(mediaKey);
                    e.currentTarget.onerror = null; // Prevents infinite loops
                    e.currentTarget.src =
                      "/images/newInspection/fallbackImage.png";
                    e.currentTarget.className =
                      "w-full h-full rounded-[15px] border object-cover";
                  }}
                />
              ) : (
                <div className="w-full h-full border rounded-[15px] relative">
                  <video 
                    className={`w-full h-full rounded-[15px] object-cover ${isLoading ? 'opacity-50' : 'opacity-100'}`}
                    onLoadedData={() => handleMediaLoaded(mediaKey)}
                    onError={() => handleMediaError(mediaKey)}
                  >
                    <source src={item.url} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                  <div className="flex justify-center items-center bg-[#12121257] w-full h-full rounded-[15px] absolute bottom-0">
                    <Image
                      src={"/images/newFindings/Vector.png"}
                      width={30}
                      height={32}
                      alt="play"
                      className="w-[30px] h-[30px]"
                    />
                  </div>
                </div>
              )}
            </div>
          );
        })}
      {media &&
        media.map((item: any, index: number) => {
          const mediaKey = getMediaKey(item.src, index);
          const isLoading = loadingStates[mediaKey];
          
          return (
            <div
              className="w-[150px] h-[150px] rounded-[15px] flex justify-center items-center object-cover cursor-pointer relative"
              key={index}
              onClick={() => handleViewMedia(item.src, item.type)}
            >
              <Tooltip title="Delete" placement="bottom">
                <Image
                  src="/images/home/<USER>"
                  width={24}
                  height={24}
                  alt="image"
                  className="w-[24px] h-[24px] object-cover rounded-lg absolute top-2 right-2 z-10 bg-white"
                  onClick={async (e) => {
                    e.stopPropagation();
                    const currentSrc = item.src;

                    // Remove from media array
                    setMedia(
                      media.filter(
                        (mediaItem: any) => mediaItem.src !== currentSrc
                      )
                    );

                    // Helper function to convert File to base64
                    const fileToBase64 = (file: File): Promise<string> => {
                      return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.readAsDataURL(file);
                        reader.onload = () => resolve(reader.result as string);
                        reader.onerror = (error) => reject(error);
                      });
                    };

                    // Find and remove the actual file from the respective array
                    if (item.type === "image") {
                      const newImagesArray = [...imagesArray];
                      for (let i = 0; i < newImagesArray.length; i++) {
                        const base64 = await fileToBase64(newImagesArray[i]);
                        if (base64 === currentSrc) {
                          newImagesArray.splice(i, 1);
                          break;
                        }
                      }
                      setImagesArray(newImagesArray);
                    } else {
                      const newVideosArray = [...videosArray];
                      for (let i = 0; i < newVideosArray.length; i++) {
                        const base64 = await fileToBase64(newVideosArray[i]);
                        if (base64 === currentSrc) {
                          newVideosArray.splice(i, 1);
                          break;
                        }
                      }
                      setVideosArray(newVideosArray);
                    }
                  }}
                />
              </Tooltip>
              
              {/* Show loader while image is loading */}
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50 rounded-[15px] z-10">
                  <Spin indicator={<LoadingOutlined spin />} />
                </div>
              )}
              
              {item.type === "image" ? (
                <Image
                  src={item.src}
                  width={150}
                  height={150}
                  alt="image"
                  className={`w-full h-full rounded-[15px] border object-cover ${isLoading ? 'opacity-50' : 'opacity-100'}`}
                  onLoad={() => handleMediaLoaded(mediaKey)}
                  onError={() => handleMediaError(mediaKey)}
                />
              ) : (
                <div className="w-full h-full border rounded-[15px] relative">
                  <video 
                    className={`w-full h-full rounded-[15px] object-cover ${isLoading ? 'opacity-50' : 'opacity-100'}`}
                    onLoadedData={() => handleMediaLoaded(mediaKey)}
                    onError={() => handleMediaError(mediaKey)}
                  >
                    <source src={item.src} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                  <div className="flex justify-center items-center bg-[#12121257] w-full h-full rounded-[15px] absolute bottom-0">
                    <Image
                      src={"/images/newFindings/Vector.png"}
                      width={30}
                      height={32}
                      alt="play"
                      className="w-[30px] h-[30px]"
                    />
                  </div>
                </div>
              )}
            </div>
          );
        })}
    </>
  );
};

export default Media;
