.spinContainer {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.358);
  z-index: 60;
}

.spinnerInner {
  border: 4.5px solid #2F80ED;
  border-radius: 50%;
  border-top: 4.5px solid rgba(69, 69, 69, 0);
  width: 20px;
  height: 20px;
  animation: spinInner 1.5s linear infinite;
  position: absolute;
}
.spinnerOuter {
  border: 4.5px solid #2F80ED;
  border-radius: 50%;
  border-top: 4.5px solid rgba(69, 69, 69, 0);
  width: 40px;
  height: 40px;
  animation: spinOuter 2s linear infinite;
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes spinInner {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes spinOuter {
  from {
    transform: rotate(360deg);
  }

  to {
    transform: rotate(0deg);
  }
}
