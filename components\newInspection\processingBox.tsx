import React from 'react'
import { useTranslation } from "react-i18next";

const ProcessingBox = ({ type }: any) => {

  const { t } = useTranslation();

    return (
      <div
        className={
          "p-2 border-2 border-dashed rounded-md flex flex-col items-center justify-center h-[80px] cursor-pointer border-blue-300  bg-blue-50 animate-pulse transition-all"
        }
      >
        <p
          className={`text-blue-500 text-[12px] font-[400] text-center mb-1.5`}
        >
          {t("Please wait")}
        </p>
        <p className={`text-blue-400 text-[10px] font-[300] text-center`}>
          {type === "decomposition" && t("Processing decomposition...")}
          {type === "finding" && t("Processing finding...")}
        </p>
      </div>
    );
  };

export default ProcessingBox