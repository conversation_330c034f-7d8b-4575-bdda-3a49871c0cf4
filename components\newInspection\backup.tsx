"use client";
import React, { useEffect, useRef, useState, useCallback } from "react";
import Navbar from "@/components/Navbar/navbar";
import Sidebar from "@/components/sidebar/sidebar";
import { <PERSON>pins } from "next/font/google";
import { <PERSON><PERSON> } from "antd";
import { useRouter } from "next/navigation";
import { useLoaderContext } from "@/context/LoaderContext";
import Loader from "@/components/loader/loader";
import { message } from "antd";
import {
  fetchAllDocuments,
  updateFinfingOrder,
} from "@/src/services/newInspection.api";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import { fetchReportTypeId } from "@/src/services/newFindings.api";
import { useSidebarContext } from "@/context/sidebarContext";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { DecompositionGroup, OrderItem, Finding } from "./types";
import {
  generateDecompTitle,
  handleInpectionDetails,
  handleMoreFileDrop,
} from "./utils";
import { useFindingContext } from "@/context/findingContext";
import ApproveModal from "./approveDecompModal/approveDecompModal";
import EditModal from "./editDecompModal/editModal";
import ApproveFindingModal from "./approveFindingModal/approveFindingModal";
import EditFindingModal from "./editFindingModal/editModal";
import { useHorizontalScroll } from "./utils";
import { prepareFinding } from "./prepareFinding.service";
import DecompColumnHeader from "./decompColumnHeader";
import FindingCardHeader from "./findingCardHeader";
import ProcessingBox from "./processingBox";
import { mapFiledsWithValue } from "@/src/services/fieldsMapping";
import { fetchInspection } from "@/src/services/inspectionDetails.api";
import { prepareDecomposition } from "./prepareDecomposition.service";
import { RightOutlined } from "@ant-design/icons";

const poppins = Poppins({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const NewInspection = () => {
  const router = useRouter();
  const { isCollapsed } = useSidebarContext();
  const [searchInspection, setSearchInspection] = useState<any>(null);
  const [refresh, setRefresh] = useState(false);

  const { loader, setLoader } = useLoaderContext();
  const { t } = useTranslation();

  const [groupedFindings, setGroupedFindings] = useState<any>([]); // Add state to store grouped findings
  const [inspectionName, setInspectionName] = useState("");
  const [orderArray, setOrderArray] = useState<OrderItem[]>([]);
  const [decompositionGroups, setDecompositionGroups] = useState<
    DecompositionGroup[]
  >([]);

  const { setWholeGroup } = useFindingContext();

  // Add state to track multiple decomposition processes

  const [isApproveDecompModalOpen, setIsApproveDecompModalOpen] =
    useState(false);
  const [isEditDecompModalOpen, setIsEditDecompModalOpen] = useState(false);

  const [isApproveFindingModalOpen, setIsApproveFindingModalOpen] =
    useState(false);
  const [isEditFindingModalOpen, setIsEditFindingModalOpen] = useState(false);
  const [numberOfProcessingDecomp, setNumberOfProcessingDecomp] = useState(0);
  const [projectName, setProjectName] = useState("");

  // Add these state variables near other state declarations
  const [processingFindingIds, setProcessingFindingIds] = useState<{
    [key: string]: number;
  }>({});

  const [loadingIds, setLoadingIds] = useState<any>([]); // for loading states of more file drops

  // =============================================================================================
  const [isDraggingFiles, setIsDraggingFiles] = useState(false);
  const [isDraggingDND, setIsDraggingDND] = useState(false); // Track DND operations
  const [isDraggingColumn, setIsDraggingColumn] = useState(false); // Track column dragging specifically
  const [isAutoScrolling, setIsAutoScrolling] = useState(false); // Track if auto-scrolling is active
  const boardScrollContainerRef = useRef<any>(null);
  const scrollIntervalRef = useRef<any>(null);
  const columnScrollIntervalRef = useRef<any>(null); // Separate interval for column drag scrolling
  const dragCounterRef = useRef(0); // Tracks drag enter/leave events
  const mousePositionRef = useRef({ x: 0, y: 0 }); // Track mouse position during column drag

  // const scrollContainerRef = useRef<HTMLDivElement | any>(null);
  const { handleMouseDown } = useHorizontalScroll(boardScrollContainerRef, {
    scrollAmount: 200, // Amount to scroll for keyboard arrows
    scrollSpeed: 2.5, // Increased speed multiplier for immediate response (was 1.5)
    isDraggingDND, // Pass DND state to prevent conflicts
  });

  const clearScrollInterval = useCallback(() => {
    if (scrollIntervalRef.current) {
      clearInterval(scrollIntervalRef.current);
      scrollIntervalRef.current = null;
    }
  }, []);

  const clearColumnScrollInterval = useCallback(() => {
    if (columnScrollIntervalRef.current) {
      clearInterval(columnScrollIntervalRef.current);
      columnScrollIntervalRef.current = null;
    }
    setIsAutoScrolling(false);
  }, []);

  // Fast auto-scroll logic for column dragging
  const handleColumnDragAutoScroll = useCallback(
    (mouseX: number) => {
      const scrollContainer = boardScrollContainerRef.current;
      if (!scrollContainer || !isDraggingColumn) return;

      const rect = scrollContainer.getBoundingClientRect();
      const containerWidth = rect.width;

      // Enhanced thresholds and speeds for faster column dragging
      const edgeThreshold = 150; // Larger threshold for better UX
      const maxScrollSpeed = 0; // Much faster than file drag (was 40)
      const minScrollSpeed = 0; // Minimum speed when at threshold edge

      // Clear any existing column scroll
      clearColumnScrollInterval();

      // Calculate distance from edges
      const leftDistance = mouseX;
      const rightDistance = containerWidth - mouseX;

      // Scroll left when near left edge
      if (leftDistance < edgeThreshold && scrollContainer.scrollLeft > 0) {
        // Progressive speed: closer to edge = faster scroll
        const speedMultiplier = Math.max(
          0.2,
          (edgeThreshold - leftDistance) / edgeThreshold
        );
        const scrollSpeed =
          minScrollSpeed + (maxScrollSpeed - minScrollSpeed) * speedMultiplier;

        setIsAutoScrolling(true);
        columnScrollIntervalRef.current = setInterval(() => {
          if (scrollContainer.scrollLeft > 0) {
            scrollContainer.scrollLeft -= scrollSpeed;
          } else {
            clearColumnScrollInterval();
          }
        }, 16); // Faster interval (was 16) for smoother scrolling
      }
      // Scroll right when near right edge
      else if (rightDistance < edgeThreshold) {
        const maxScroll =
          scrollContainer.scrollWidth - scrollContainer.clientWidth;
        if (scrollContainer.scrollLeft < maxScroll) {
          // Progressive speed: closer to edge = faster scroll
          const speedMultiplier = Math.max(
            0.2,
            (edgeThreshold - rightDistance) / edgeThreshold
          );
          const scrollSpeed =
            minScrollSpeed +
            (maxScrollSpeed - minScrollSpeed) * speedMultiplier;

          setIsAutoScrolling(true);
          columnScrollIntervalRef.current = setInterval(() => {
            if (scrollContainer.scrollLeft < maxScroll) {
              scrollContainer.scrollLeft += scrollSpeed;
            } else {
              clearColumnScrollInterval();
            }
          }, 8); // Faster interval for smoother scrolling
        }
      } else {
        // Not near edges, stop auto-scrolling
        clearColumnScrollInterval();
      }
    },
    [isDraggingColumn, clearColumnScrollInterval]
  );

  // Track mouse movement during column drag
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDraggingColumn) {
        const scrollContainer = boardScrollContainerRef.current;
        if (scrollContainer) {
          const rect = scrollContainer.getBoundingClientRect();
          const mouseX = e.clientX - rect.left;
          mousePositionRef.current = { x: mouseX, y: e.clientY - rect.top };
          handleColumnDragAutoScroll(mouseX);
        }
      }
    },
    [isDraggingColumn, handleColumnDragAutoScroll]
  );

  //============================================================================================

  const handleOnDrop = (e: React.DragEvent<HTMLDivElement>) => {
    // Check if the drop target is a DropBox component or its children
    const target = e.target as HTMLElement;
    const isDropBox = target.closest('[data-dropbox="true"]');

    // If dropped on a DropBox, don't prevent default and let the DropBox handle it
    if (isDropBox) {
      dragCounterRef.current = 0;
      setIsDraggingFiles(false);
      clearScrollInterval();
      return; // Don't prevent default, let the event bubble to DropBox
    }

    // Only prevent default for non-DropBox drops
    e.preventDefault();
    dragCounterRef.current = 0;
    setIsDraggingFiles(false);
    clearScrollInterval();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    // Check if dragging files (not internal cards)
    if (e.dataTransfer?.types?.includes("Files")) {
      setIsDraggingFiles(true);
      dragCounterRef.current++;
    }
  };

  const handleDragOverScroll = (e: React.DragEvent<HTMLDivElement>) => {
    // Check if the drag is over a DropBox component
    const target = e.target as HTMLElement;
    const isDropBox = target.closest('[data-dropbox="true"]');

    // If over a DropBox, don't prevent default to allow DropBox to handle it
    if (isDropBox) {
      return;
    }

    e.preventDefault();

    if (!isDraggingFiles) return;

    const scrollContainer = boardScrollContainerRef.current;
    const rect = scrollContainer.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const threshold = 400; // Distance from edge to trigger scroll
    const scrollSpeed = 20;

    // Clear existing scroll
    clearScrollInterval();

    // Scroll left when near left edge
    if (mouseX < threshold && scrollContainer.scrollLeft > 0) {
      scrollIntervalRef.current = setInterval(() => {
        scrollContainer.scrollLeft -= scrollSpeed;
      }, 16);
    }
    // Scroll right when near right edge
    else if (mouseX > rect.width - threshold) {
      const maxScroll =
        scrollContainer.scrollWidth - scrollContainer.clientWidth;
      if (scrollContainer.scrollLeft < maxScroll) {
        scrollIntervalRef.current = setInterval(() => {
          scrollContainer.scrollLeft += scrollSpeed;
        }, 16);
      }
    }
  };

  const handleDragLeaveScroll = () => {
    dragCounterRef.current--;
    // Only stop when completely leaving the container
    if (dragCounterRef.current === 0) {
      setIsDraggingFiles(false);
      clearScrollInterval();
    }
  };

  useEffect(() => {
    localStorage.removeItem("findingId");
    localStorage.removeItem("isFindingUpdate");
    localStorage.removeItem("isMeasureUpdate");
    localStorage.removeItem("isAddingMeasure");
    localStorage.removeItem("addNew");
    localStorage.removeItem("parent_finding_id");
    localStorage.removeItem("updateFindingObj");
    localStorage.removeItem("decompositionItemMedia");
    localStorage.removeItem("group");
    localStorage.removeItem("parentId");
    localStorage.removeItem("FindingTypeId");
  }, []);

  // Setup mouse event listeners for column drag tracking
  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
    };
  }, [handleMouseMove]);

  // Cleanup effect for DND and scroll states
  useEffect(() => {
    return () => {
      clearScrollInterval();
      clearColumnScrollInterval();
      setIsDraggingDND(false);
      setIsDraggingFiles(false);
      setIsDraggingColumn(false);
      setIsAutoScrolling(false);
    };
  }, [clearScrollInterval, clearColumnScrollInterval]);

  useEffect(() => {
    const name = localStorage.getItem("ScalarInspectionName");
    const storedProjectName = localStorage.getItem("ScalarProjectName");

    if (name) {
      setInspectionName(name);
    }

    if (storedProjectName) {
      setProjectName(storedProjectName);
    }
  }, []);

  const fetchFindings = async () => {
    // setLoader(true);
    const inspectionId = localStorage.getItem("ScalarInspectionId");
    const fetchedFindings: any = await fetchAllDocuments(inspectionId); // Fetch findings from the API
    const inspectionDetails: any = await fetchInspection(inspectionId);

    const decompId = process.env.NEXT_DECOMP_ID;
    const findingId = process.env.NEXT_FINDING_ID;

    const allFindings = fetchedFindings.map((finding: any) => {
      const findingTypeId = finding?.finding_type?.id;
      const suggestionField =
        findingTypeId === decompId
          ? JSON.parse(inspectionDetails?.decomp_fields)?.fields
          : findingTypeId === findingId
          ? JSON.parse(inspectionDetails?.finding_fields)?.fields
          : JSON.parse(inspectionDetails?.measure_fields)?.fields;

      const mappedFields = mapFiledsWithValue(suggestionField, finding?.fields);
      return {
        ...finding,
        fields: JSON.stringify(mappedFields),
      };
    });

    if (allFindings) {
      // Function to build hierarchical structure for findings
      const buildHierarchy = (parentId: string | null): Finding[] => {
        // Find all findings that have the given parentId
        const findings = allFindings.filter(
          (finding: any) => finding.parent_finding_id === parentId
        );

        // Recursively build children for each finding
        return findings.map((finding: any) => ({
          ...finding,
          children: buildHierarchy(finding.id), // Recursively find all children
        }));
      };

      // Create the grouped structure with all parents and their descendants
      const grouped = buildHierarchy(null).map((parent) => ({
        id: parent.id,
        parent: parent,
        title_fields: parent.title_fields,
        decompositionItem: flattenFindings(parent), // Flatten all descendants
      }));

      const groupedWithoutChildrenArray = grouped.map((group: any) => {
        const items = group.decompositionItem.map((i: any) => {
          const { children, ...rest } = i;
          return rest;
        });

        const { children, ...rest } = group.parent;

        return {
          ...group,
          decompositionItem: items,
          parent: rest,
        };
      });

      setGroupedFindings(groupedWithoutChildrenArray);

      setLoader(false);
    } else {
      setLoader(false);
      message.error(t("Something went wrong!"));
    }
  };

  // Helper function to flatten all children into a single-level array
  const flattenFindings = (finding: any): Finding[] => {
    const children = finding.children ?? [];
    return [finding, ...children.flatMap(flattenFindings)];
  };

  const getInspectionTypeId = async (id: any) => {
    setLoader(true);
    const res: any = await fetchReportTypeId(id);
    if (res) {
      localStorage.setItem("reportTypeId", res);
      setLoader(false);
    } else {
      setLoader(false);
      message.error(t("Error fetching Inspection types."));
    }
  };

  useEffect(() => {
    if (groupedFindings.length > 0) {
      fetchFindings();
    }
  }, [refresh]);

  // Use Effect for fetch all findings and inspecction type id
  useEffect(() => {
    const laguage: any = localStorage.getItem("ScalarLanguage");
    handleChangeLanguage(laguage);
    localStorage.removeItem("ViewInspectionDetails");
    fetchFindings();
    const inspection_id = localStorage.getItem("ScalarInspectionId");
    getInspectionTypeId(inspection_id);
  }, []);

  // Use effect for order changes
  useEffect(() => {
    if (groupedFindings.length > 0) {
      // Transform groupedFindings into decompositionGroups
      const groups = groupedFindings.map((group: any) => ({
        id: group.id,
        title: generateDecompTitle(
          group.title_fields,
          group.decompositionItem[0].fields
        ),
        decomposition: group.parent,
        findings: group.decompositionItem.filter(
          (item: any) =>
            item?.finding_type?.id === process.env.NEXT_FINDING_ID ||
            item?.finding_type?.id === "83mi507Lv3BizK7bJ7Hg"
        ),
        isApproved: group?.parent?.approved,
      }));

      console.log("groups", groups);

      // Generate initial order array
      const findingOrderString: any = localStorage.getItem("findingOrder");
      if (findingOrderString) {
        const findingOrder = JSON.parse(findingOrderString);

        // Create a comprehensive mapping of all items first
        const newOrder: OrderItem[] = [];
        groupedFindings.forEach((group: any) => {
          // Add decomposition
          newOrder.push({
            id: group.id,
            type: "decomposition",
            parentId: null,
          });

          // Add findings and their measures
          group.decompositionItem.forEach((finding: any) => {
            if (
              finding?.finding_type?.id === process.env.NEXT_FINDING_ID ||
              finding?.finding_type?.id === "83mi507Lv3BizK7bJ7Hg"
            ) {
              newOrder.push({
                id: finding.id,
                type: "finding",
                parentId: group.id,
              });

              // Add measures
              const measures = group.decompositionItem.filter(
                (item: any) => item.parent_finding_id === finding.id
              );
              measures.forEach((measure: any) => {
                newOrder.push({
                  id: measure.id,
                  type: "measure",
                  parentId: finding.id,
                });
              });
            }
          });
        });

        // Create an ordered array based on the saved findingOrder
        const initialOrder: OrderItem[] = [];

        // First add all items that exist in the saved order
        findingOrder.forEach((id: string) => {
          const item = newOrder.find((i) => i.id === id);
          if (item) {
            initialOrder.push(item);
          }
        });

        // Then add any new items that weren't in the saved order
        newOrder.forEach((item) => {
          if (!initialOrder.some((i) => i.id === item.id)) {
            initialOrder.push(item);
          }
        });

        setOrderArray(initialOrder);

        // REORDERING BOTH GROUPS AND FINDINGS WITHIN GROUPS

        // 1. First, reorder the decompositionGroups based on the findingOrder
        const orderedGroups: DecompositionGroup[] = [];
        const processedGroupIds = new Set<string>();

        // Extract unique group IDs from the order, maintaining their order
        initialOrder.forEach((item) => {
          if (
            item.type === "decomposition" &&
            !processedGroupIds.has(item.id)
          ) {
            processedGroupIds.add(item.id);
            const group = groups.find((g: any) => g.id === item.id);
            if (group) {
              // Deep clone the group to modify its findings
              orderedGroups.push({ ...group, findings: [...group.findings] });
            }
          }
        });

        // Add any groups not found in the order
        groups.forEach((group: any) => {
          if (!processedGroupIds.has(group.id)) {
            orderedGroups.push({ ...group, findings: [...group.findings] });
          }
        });

        // 2. For each group, reorder its findings based on findingOrder
        orderedGroups.forEach((group) => {
          // Get all findings for this group from initialOrder to determine their sequence
          const findingsInOrder = initialOrder
            .filter(
              (item) => item.type === "finding" && item.parentId === group.id
            )
            .map((item) => item.id);

          // Create a map for efficient lookup
          const findingMap = new Map(
            group.findings.map((finding) => [finding.id, finding])
          );

          // Create a new ordered findings array
          const orderedFindings: any[] = [];

          // First add findings in the order they appear in findingOrder
          findingsInOrder.forEach((id) => {
            const finding = findingMap.get(id);
            if (finding) {
              orderedFindings.push(finding);
              findingMap.delete(id);
            }
          });

          // Add any remaining findings that weren't in the order
          findingMap.forEach((finding) => {
            orderedFindings.push(finding);
          });

          // Update the group's findings with the ordered list
          group.findings = orderedFindings;
        });

        setDecompositionGroups(orderedGroups);
      } else {
        // Default ordering if no saved order exists
        const initialOrder: OrderItem[] = [];
        groupedFindings.forEach((group: any) => {
          // Add decomposition
          initialOrder.push({
            id: group.id,
            type: "decomposition",
            parentId: null,
          });

          // Add findings and their measures
          group.decompositionItem.forEach((finding: any) => {
            if (
              finding.finding_type.id === process.env.NEXT_FINDING_ID ||
              finding.finding_type.id === "83mi507Lv3BizK7bJ7Hg"
            ) {
              initialOrder.push({
                id: finding.id,
                type: "finding",
                parentId: group.id,
              });

              // Add measures
              const measures = group.decompositionItem.filter(
                (item: any) => item.parent_finding_id === finding.id
              );
              measures.forEach((measure: any) => {
                initialOrder.push({
                  id: measure.id,
                  type: "measure",
                  parentId: finding.id,
                });
              });
            }
          });
        });

        setOrderArray(initialOrder);
        setDecompositionGroups(groups);
      }
    }
  }, [groupedFindings]);

  // Add this effect to react to changes in localStorage findingOrder
  useEffect(() => {
    const handleStorageChange = () => {
      const findingOrderString = localStorage.getItem("findingOrder");
      if (findingOrderString) {
        const findingOrder = JSON.parse(findingOrderString);

        // Rebuild the orderArray based on the updated findingOrder
        const newOrderArray: OrderItem[] = [];

        // Map IDs to OrderItem objects
        findingOrder.forEach((id: string) => {
          // Try to find existing item first
          const existingItem = orderArray.find((item) => item.id === id);
          if (existingItem) {
            newOrderArray.push(existingItem);
          } else {
            // This is a new item, try to determine its type and parentId
            // For measures, we need to look in groupedFindings
            const item = findMeasureInGroupedFindings(id);
            if (item) {
              newOrderArray.push({
                id,
                type: "measure",
                parentId: item.parent_finding_id,
              });
            }
          }
        });

        setOrderArray(newOrderArray);
      }
    };

    // Helper function to find a measure in groupedFindings
    const findMeasureInGroupedFindings = (id: string) => {
      for (const group of groupedFindings) {
        const item = group.decompositionItem.find(
          (item: any) => item.id === id
        );
        if (item) return item;
      }
      return null;
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [groupedFindings]);

  // Function for handling dnd start
  const handleDragStart = (result: any) => {
    setIsDraggingDND(true);

    // Check if we're dragging a column
    if (result.type === "column") {
      setIsDraggingColumn(true);
    }

    // Clear any ongoing scroll intervals when DND starts
    clearScrollInterval();
    clearColumnScrollInterval();
  };

  // Function for handdling dnd
  const handleDragEnd = (result: any) => {
    setIsDraggingDND(false);
    setIsDraggingColumn(false);

    // Clear all scroll intervals
    clearScrollInterval();
    clearColumnScrollInterval();

    const { destination, source, type } = result;

    if (!destination) return;

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    if (type === "column") {
      // Handle decomposition reordering
      const newGroups = Array.from(decompositionGroups);
      const [removed] = newGroups.splice(source.index, 1);
      newGroups.splice(destination.index, 0, removed);
      setDecompositionGroups(newGroups);

      // Build a completely new order array preserving the parent-child relationships
      const newOrderArray: OrderItem[] = [];

      // First, create a map of all findings and their measures for quick lookup
      const findingMeasuresMap: Record<string, OrderItem[]> = {};
      orderArray.forEach((item: any) => {
        if (item.type === "measure") {
          if (!findingMeasuresMap[item.parentId]) {
            findingMeasuresMap[item.parentId] = [];
          }
          findingMeasuresMap[item.parentId].push(item);
        }
      });

      // Process decompositions in their new order
      newGroups.forEach((group) => {
        // Add the decomposition itself
        newOrderArray.push({
          id: group.id,
          type: "decomposition",
          parentId: null,
        });

        // Add all findings for this decomposition
        const findingsForThisDecomp = orderArray.filter(
          (item) => item.type === "finding" && item.parentId === group.id
        );

        // Add each finding followed by its measures
        findingsForThisDecomp.forEach((finding) => {
          newOrderArray.push(finding);

          // Add all measures for this finding
          const measuresForThisFinding = findingMeasuresMap[finding.id] || [];
          newOrderArray.push(...measuresForThisFinding);
        });
      });

      setOrderArray(newOrderArray);

      const newOrderIds = newOrderArray.map((item: any) => item.id);
      updateFinfingOrder(newOrderIds);
    } else if (type === "task") {
      // Handle finding reordering within same decomposition
      if (source.droppableId === destination.droppableId) {
        const group = decompositionGroups.find(
          (g) => g.id === source.droppableId
        );
        if (!group) return;

        const newFindings = Array.from(group.findings);
        const [removed] = newFindings.splice(source.index, 1);
        newFindings.splice(destination.index, 0, removed);

        const newGroups = decompositionGroups.map((g) =>
          g.id === source.droppableId ? { ...g, findings: newFindings } : g
        );
        setDecompositionGroups(newGroups);

        // Create a completely new order array while maintaining the relationship between findings and measures
        const newOrderArray: OrderItem[] = [];

        // Create a map of all findings and their measures for quick lookup
        const findingMeasuresMap: Record<string, OrderItem[]> = {};
        orderArray.forEach((item: any) => {
          if (item.type === "measure") {
            if (!findingMeasuresMap[item.parentId]) {
              findingMeasuresMap[item.parentId] = [];
            }
            findingMeasuresMap[item.parentId].push(item);
          }
        });

        // Identify all the decompositions
        const decomps = orderArray.filter(
          (item) => item.type === "decomposition"
        );

        // For each decomposition
        decomps.forEach((decomp) => {
          // Add the decomposition
          newOrderArray.push(decomp);

          if (decomp.id === source.droppableId) {
            // This is the decomposition where findings were reordered
            // Get all findings for this decomposition in their new order
            const findingsInNewOrder = newFindings
              .map((finding) => {
                return orderArray.find(
                  (item) => item.type === "finding" && item.id === finding.id
                );
              })
              .filter(Boolean) as OrderItem[];

            // Add each finding followed by its measures
            findingsInNewOrder.forEach((finding) => {
              newOrderArray.push(finding);

              // Add all measures for this finding
              const measuresForThisFinding =
                findingMeasuresMap[finding.id] || [];
              newOrderArray.push(...measuresForThisFinding);
            });
          } else {
            // For other decompositions, maintain the existing order
            const findingsForThisDecomp = orderArray.filter(
              (item) => item.type === "finding" && item.parentId === decomp.id
            );

            findingsForThisDecomp.forEach((finding) => {
              newOrderArray.push(finding);

              // Add all measures for this finding
              const measuresForThisFinding =
                findingMeasuresMap[finding.id] || [];
              newOrderArray.push(...measuresForThisFinding);
            });
          }
        });

        setOrderArray(newOrderArray);
        const newOrderIds = newOrderArray.map((item: any) => item.id);
        updateFinfingOrder(newOrderIds);
      }
    }
  };

  // Updated DropBox component with simplified UI states
  const DropBox = ({
    type,
    parentId,
    text,
  }: {
    type: "decomposition" | "finding";
    parentId?: string;
    text: string;
  }) => {
    const fileInputRef = React.useRef<HTMLInputElement>(null);

    const handleClick = () => {
      console.log("DropBox clicked", type);
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
      console.log("File selected", e.target.files);
      if (e.target.files && e.target.files.length > 0) {
        const files = Array.from(e.target.files);
        if (type === "decomposition") {
          setNumberOfProcessingDecomp((prev) => prev + 1);
          prepareDecomposition(
            files,
            type,
            setNumberOfProcessingDecomp,
            setOrderArray,
            orderArray,
            decompositionGroups,
            setLoader,
            t,
            fetchFindings
          );
        } else if (parentId) {
          prepareFinding(
            files,
            parentId,
            setProcessingFindingIds,
            setOrderArray,
            orderArray,
            decompositionGroups,
            setLoader,
            t,
            fetchFindings
          );
        }
      }
    };

    const handleFileDrop = (event: React.DragEvent) => {
      event.preventDefault();
      event.stopPropagation(); // Prevent event from bubbling up to board handlers
      const files = Array.from(event.dataTransfer.files);
      console.log("DropBox handleFileDrop called with files:", files);
      console.log("DropBox type:", type, "parentId:", parentId);

      if (type === "decomposition") {
        setNumberOfProcessingDecomp((prev) => prev + 1);
        prepareDecomposition(
          files,
          type,
          setNumberOfProcessingDecomp,
          setOrderArray,
          orderArray,
          decompositionGroups,
          setLoader,
          t,
          fetchFindings
        );
      } else if (parentId) {
        prepareFinding(
          files,
          parentId,
          setProcessingFindingIds,
          setOrderArray,
          orderArray,
          decompositionGroups,
          setLoader,
          t,
          fetchFindings
        );
      }
    };

    // Determine the styling based on state
    let boxStyle =
      "p-2 border-2 border-dashed rounded-md flex flex-col items-center justify-center h-[80px] cursor-pointer transition-colors transition-all border-orange-300 bg-orange-50 hover:bg-orange-100 transition-all hover:z-50";
    let textColor = "text-orange-500";
    let subTextColor = "text-orange-400";

    return (
      <div
        className={boxStyle}
        data-dropbox="true"
        onDrop={handleFileDrop}
        onDragOver={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onClick={(e) => {
          e.stopPropagation();
          handleClick();
        }}
        style={{ cursor: "pointer", position: "relative" }}
      >
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileSelect}
          accept="image/*,video/*"
          multiple
        />
        <div className="flex flex-col items-center">
          {/* <div className="flex justify-center items-center text-[14px] border-2 border-orange-400 text-orange-400 w-[20px] h-[20px] rounded-full pt-[2px] pl-[1px] mb-2">
            +
          </div> */}
          <p
            className={`${textColor} text-[12px] font-[400] text-center mb-1.5`}
          >
            {text}
          </p>
          <p className={`${subTextColor} text-[10px] font-[300] text-center`}>
            {t("Drop image or click here")}
          </p>
        </div>
        <button
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-30"
          onClick={(e) => {
            e.stopPropagation();
            handleClick();
          }}
        />
      </div>
    );
  };

  return (
    <>
      {loader && <Loader />}
      <div className="flex h-screen">
        <Sidebar />
        <div
          className={`${
            isCollapsed ? "w-[calc(100vw-60px)]" : "w-[calc(100vw-16%)]"
          }`}
        >
          <Navbar
            search={false}
            searchInspection={searchInspection}
            setSearch={setSearchInspection}
          />
          <div
            className={`h-[calc(100%-60px)] text-black ${poppins.className} overflow-auto scrollbar`}
          >
            <div className="w-full flex justify-between items-center px-10 py-6 mb-3 sticky top-0 z-10 bg-white border-b">
              {/* <h1 className="text-[24px] leading-[24px] font-[500] pt-2">
                {projectName && (
                  <span
                    className="cursor-pointer hover:underline"
                    onClick={() => router.push("/project")}
                  >
                    {projectName}
                  </span>
                )}
                {projectName && " > "}
                {inspectionName !== "" ? inspectionName : t("New Inspections")}
              </h1> */}
              <h1 className="text-[24px] leading-[24px] font-[500]">
                <span
                  className="cursor-pointer hover:underline"
                  onClick={() =>
                    router.push(projectName ? "/project" : "/home")
                  }
                >
                  {projectName || t("Projects")}
                </span>{" "}
                <RightOutlined className="text-[18px]" />{" "}
                {inspectionName !== "" ? inspectionName : t("New Inspections")}
              </h1>

              <div className="flex gap-3">
                <Button
                  // onClick={() => router.push("/project")}
                  onClick={() => {
                    const hasProject =
                      localStorage.getItem("ScalarProjectName");
                    router.push(hasProject ? "/project" : "/home");
                  }}
                  className="text-[14px] h-[44px] px-6 text-[#2F80ED] border-[#2F80ED] border leading-[14px] font-[500] bg-[#2F80ED] bg-opacity-5 flex items-center gap-2 p-3 rounded-[10px]"
                >
                  {t("Back")}
                </Button>
                <Button
                  onClick={() => handleInpectionDetails(router, inspectionName)}
                  className="text-[14px] h-[44px] px-4 text-[#2F80ED] border-[#2F80ED] border leading-[14px] font-[500] bg-[#2F80ED] bg-opacity-5 flex items-center gap-2 p-3 rounded-[10px]"
                >
                  {t("Inspection Details")}
                </Button>
                <div className="relative">
                  <Button
                    onClick={() =>
                      router.push("/newInspection/findingOverview")
                    }
                    type="primary"
                    className={`${poppins.className} custom-button px-8 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px]`}
                  >
                    <p className={`text-white text-[14px] font-[400]`}>
                      {t("Next")}
                    </p>
                  </Button>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="flex flex-col h-[calc(100vh-170px)]">
                <DragDropContext
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                >
                  <div
                    className={`flex-grow overflow-x-auto overflow-y-hidden pointer-events-auto ${
                      isAutoScrolling
                        ? "ring-2 ring-blue-400 ring-opacity-50"
                        : ""
                    }`}
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      scrollBehavior: isDraggingColumn ? "auto" : "smooth", // Disable smooth scrolling during column drag for faster response
                      transition: isAutoScrolling
                        ? "box-shadow 0.2s ease-in-out"
                        : "none",
                    }}
                    ref={boardScrollContainerRef}
                    onMouseDown={!isDraggingDND ? handleMouseDown : undefined}
                    onDragEnter={handleDragEnter}
                    onDragLeave={handleDragLeaveScroll}
                    onDragOver={handleDragOverScroll}
                    onDrop={handleOnDrop}
                  >
                    <Droppable
                      droppableId="all-columns"
                      direction="horizontal"
                      type="column"
                    >
                      {(provided) => (
                        <div
                          className="flex gap-4 min-h-full pb-4"
                          style={{
                            userSelect: "none",
                            minWidth: "fit-content", // Ensure container expands with content
                            width: "max-content", // Allow container to grow beyond viewport
                          }}
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                        >
                          {decompositionGroups.map((group, index) => (
                            <Draggable
                              key={group.id}
                              draggableId={group.id}
                              index={index}
                            >
                              {(provided) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  className={`w-[180px] flex-shrink-0 bg-white rounded-lg shadow border flex flex-col h-fit max-h-[calc(100vh-196px)] ${
                                    index === 0 && "ml-4"
                                  }`}
                                  style={{
                                    ...provided.draggableProps.style,
                                  }}
                                  onDrop={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    if (!group.isApproved) {
                                      handleMoreFileDrop(
                                        e,
                                        group.id,
                                        group?.decomposition?.media,
                                        setLoadingIds,
                                        fetchFindings
                                      );
                                    }
                                  }}
                                  onDragOver={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                  }}
                                  onMouseDown={(e) => {
                                    e.stopPropagation();
                                  }}
                                >
                                  {/* Column header */}
                                  <DecompColumnHeader
                                    {...{
                                      group,
                                      provided,
                                      groupedFindings,
                                      setWholeGroup,
                                      setIsEditDecompModalOpen,
                                      setIsApproveDecompModalOpen,
                                      loadingIds,
                                    }}
                                  />

                                  {/* Column content */}
                                  <Droppable droppableId={group.id} type="task">
                                    {(provided) => (
                                      <div
                                        ref={provided.innerRef}
                                        {...provided.droppableProps}
                                        className="m-2 mr-1 p-0 overflow-y-auto scrollbar-mini !cursor-default"
                                        style={{
                                          maxHeight: "calc(100vh - 240px)",
                                        }}
                                        onClick={(e) => {
                                          e.preventDefault();
                                          e.stopPropagation();
                                        }}
                                      >
                                        {group.findings &&
                                          group.findings.map(
                                            (finding, index) => (
                                              <Draggable
                                                key={finding.id}
                                                draggableId={finding.id}
                                                index={index}
                                              >
                                                {(provided, snapshot) => (
                                                  <FindingCardHeader
                                                    {...{
                                                      finding,
                                                      provided,
                                                      snapshot,
                                                      groupedFindings,
                                                      setWholeGroup,
                                                      setIsEditFindingModalOpen,
                                                      setIsApproveFindingModalOpen,
                                                      loadingIds,
                                                      handleMoreFileDrop,
                                                      group,
                                                      setLoadingIds,
                                                      fetchFindings,
                                                    }}
                                                  />
                                                )}
                                              </Draggable>
                                            )
                                          )}
                                        {provided.placeholder}

                                        {/* Show processing findings for this decomposition */}
                                        {processingFindingIds[group.id] > 0 &&
                                          Array.from({
                                            length:
                                              processingFindingIds[group.id],
                                          }).map((_, idx) => (
                                            <div
                                              key={`processing-finding-${group.id}-${idx}`}
                                              className=" mr-1 mb-2 rounded-md shadow-sm bg-blue-50 text-blue-600 animate-pulse"
                                            >
                                              <ProcessingBox type="finding" />
                                            </div>
                                          ))}

                                        {/* Only show the finding upload box if the decomposition is approved */}
                                        {group.isApproved && (
                                          <div
                                            className="sticky bottom-0 w-full pt-2 pr-1 bg-white"
                                            style={{ zIndex: 10 }}
                                          >
                                            <div
                                              onClick={(e) =>
                                                e.stopPropagation()
                                              }
                                            >
                                              <DropBox
                                                type="finding"
                                                parentId={group.id}
                                                text={`+ ${t("Add Finding")}`}
                                              />
                                            </div>
                                          </div>
                                        )}

                                        {/* Show pending status if not approved */}
                                      </div>
                                    )}
                                  </Droppable>
                                  {!group.isApproved && (
                                    <div className="text-[12px] text-center relative bottom-1.5 text-gray-500">
                                      {t("Waiting for approval")}
                                    </div>
                                  )}
                                </div>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}

                          {Array.from({ length: numberOfProcessingDecomp }).map(
                            (_, index) => (
                              <div
                                key={index}
                                className={`w-[180px] text-[12px] flex-shrink-0 rounded-lg shadow border flex flex-col h-fit ${
                                  decompositionGroups.length === 0 ? "ml-4" : ""
                                }`}
                              >
                                <div className="p-2">
                                  <ProcessingBox type="decomposition" />
                                </div>
                              </div>
                            )
                          )}

                          {/* Add new decomposition box as the last column */}
                          {!loader && (
                            <div
                              className={`w-[196px] pr-4 ${
                                decompositionGroups.length === 0 &&
                                numberOfProcessingDecomp === 0
                                  ? "pl-4"
                                  : "pl-0"
                              }`}
                            >
                              <div
                                className="w-[180px] text-[12px] flex-shrink-0 rounded-lg shadow border flex flex-col h-fit"
                                style={{ position: "relative", zIndex: 10 }}
                              >
                                <div
                                  className="p-2"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <DropBox
                                    type="decomposition"
                                    text={`+ ${t("New decomp item")}`}
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </Droppable>
                  </div>
                </DragDropContext>
              </div>
            </div>
          </div>
        </div>
      </div>
      {isApproveDecompModalOpen && (
        <ApproveModal
          {...{
            isModalOpen: isApproveDecompModalOpen,
            setIsModalOpen: setIsApproveDecompModalOpen,
            setAllGroups: setDecompositionGroups,
            allGroups: decompositionGroups,
            orderArray: orderArray,
            setOrderArray: setOrderArray,
            setRefresh: setRefresh,
          }}
        />
      )}
      {isEditDecompModalOpen && (
        <EditModal
          {...{
            isModalOpen: isEditDecompModalOpen,
            setIsModalOpen: setIsEditDecompModalOpen,
            setAllGroups: setDecompositionGroups,
            allGroups: decompositionGroups,
            orderArray: orderArray,
            setOrderArray: setOrderArray,
            setRefresh: setRefresh,
          }}
        />
      )}
      {isApproveFindingModalOpen && (
        <ApproveFindingModal
          {...{
            isModalOpen: isApproveFindingModalOpen,
            setIsModalOpen: setIsApproveFindingModalOpen,
            setAllGroups: setDecompositionGroups,
            allGroups: decompositionGroups,
            setGroupedFindings: setGroupedFindings,
            orderArray: orderArray,
            setOrderArray: setOrderArray,
            setRefresh: setRefresh,
            groupedFindings: groupedFindings,
          }}
        />
      )}
      {isEditFindingModalOpen && (
        <EditFindingModal
          {...{
            isModalOpen: isEditFindingModalOpen,
            setIsModalOpen: setIsEditFindingModalOpen,
            setAllGroups: setDecompositionGroups,
            allGroups: decompositionGroups,
            orderArray: orderArray,
            setOrderArray: setOrderArray,
            setRefresh: setRefresh,
            setGroupedFindings,
          }}
        />
      )}
    </>
  );
};

export default NewInspection;
