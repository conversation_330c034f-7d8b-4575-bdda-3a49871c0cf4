'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Poppins } from 'next/font/google';
import { TodoInspections } from '@/src/services/newInspection.api';
import { Mo<PERSON>, Button, message } from 'antd';

const poppins = Poppins({ weight: '400', subsets: ['latin'] });

interface MarkTodoModalProps {
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
  setMenu: (open: boolean) => void;
  setSearchedInspections: any;
  selectedInspections: string[];
  setRefresh: React.Dispatch<React.SetStateAction<boolean>>;
}

const MarkTodoModal: React.FC<MarkTodoModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  selectedInspections,
  setRefresh,
  setMenu,
  setSearchedInspections
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const markTodoInspections = async () => {
    try {
      setLoading(true);

      if (selectedInspections.length > 0) {
        const res = await TodoInspections(selectedInspections);

        if (res) {
          message.success(t('Successfully marked as todo.'));
        } else {
          message.warning(t('Some inspections failed to mark as todo.'));
        }
        setSearchedInspections((prev: any) => prev.map((inspection: any) => {
          if (selectedInspections.includes(inspection.id)) {
            return {
              ...inspection,
              isCompleted: false
            }
          } else return inspection
        }))
        // setRefresh((prev) => !prev);
      }
    } catch (error) {
      message.error(t('Something went wrong, try again later!'));
    } finally {
      setLoading(false);
      setIsModalOpen(false);
      setMenu(false)
    }
  };

  return (
    <Modal
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      centered
      className={`${poppins.className} custom-todo-modal`}
      width={350}
    >
      <div className="relative">

        <h1 className="mt-6 text-left text-[25px] leading-[52.08px] font-[600]">
          {t('Todo')} {t('Inspections')}!
        </h1>

        <p className="text-[18px] text-left mt-2">
          {t('Are you sure you want to mark')} <br />
          {t('Inspections')} {t('as todo')}?
        </p>

        <div className="flex justify-between gap-4 mt-10">
          <Button
            type="default"
            className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] bg-[#ff910020] hover:bg-[#ff910015]"
            onClick={() => setIsModalOpen(false)}
          >
            {t('Cancel')}
          </Button>

          <Button
            type="primary"
            className="w-[50%] h-[45px] text-[14px] border-none"
            style={{
              backgroundColor: '#FF9200',
              color: 'white',
            }}
            loading={loading}
            onClick={markTodoInspections}
          >
            {t('Mark Todo')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default MarkTodoModal;
