// LocationModal.tsx
import React from "react";
import { Modal } from "antd";
import LocationPicker from "./locationPicker";
import { useTranslation } from "react-i18next";
import { Libraries, useLoadScript } from '@react-google-maps/api';

const libraries: Libraries = ['places'];

const LocationModal = ({
  locationModalOpen,
  setLocationModalOpen,
  location,
  setLocation,
  googleMapsApiKey,
  isEdit
}: any) => {

  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey,
    libraries,
  });

  const { t } = useTranslation();

  // Save button will close the modal and save the location
  const handleSaveLocation = () => {
    if (!location) {
      // If the user did not drag the pin, use current location
      navigator.geolocation.getCurrentPosition((position) => {
        const userLocation = {
          lat: position.coords.latitude,
          lon: position.coords.longitude,
        };
        setLocation(userLocation.lat, userLocation.lon);
      });
    }
    // if (isEdit) {
    //   setLocation(location.lat, location.lon)
    // }
    setLocationModalOpen(false);
  };

  return (
    <Modal
      title={
        <h1 className="text-[24px] leading-[24px] font-[500] mb-4">
          {t("Set Inspection Location")}
        </h1>
      }
      centered
      open={locationModalOpen}
      onOk={handleSaveLocation} // Save the location on click
      onCancel={() => setLocationModalOpen(false)} // Close the modal without saving
      className="w-full"
      width="80%"
      okText={t("Save")}
      okButtonProps={{
        className:
          "h-[35px] px-6 text-[16px] leading-[24px] text-[#FFFFFF] font-[400] hover:bg-[#F0142F] border-none rounded-[10px] custom-ok-button",
      }}
      cancelButtonProps={{
        className: "hidden",
      }}
    >
      {/* Location Picker */}
      <div className="w-full h-[70vh] flex justify-center items-center">
        <LocationPicker
          googleMapsApiKey={googleMapsApiKey}
          location={location}
          setLocation={setLocation}
          isLoaded={isLoaded}
          loadError={loadError}
        />
      </div>
      <div className="mt-2 text-red-500">{t("Drag and drop red pin to select the location.")}</div>
    </Modal>
  );
};

export default LocationModal;
