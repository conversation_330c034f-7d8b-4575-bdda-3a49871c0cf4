.spinContainer {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0);
  z-index: 60;
}

.spinnerInner {
  border: 4px solid #f3f3f3;
  border-radius: 50%;
  border-top: 4px solid rgba(69, 69, 69, 0);
  width: 15px;
  height: 15px;
  animation: spinInner 1.5s linear infinite;
  position: absolute;
}
.spinnerOuter {
  border: 4px solid #f3f3f3;
  border-radius: 50%;
  border-top: 4px solid rgba(69, 69, 69, 0);
  width: 30px;
  height: 30px;
  animation: spinOuter 2s linear infinite;
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes spinInner {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes spinOuter {
  from {
    transform: rotate(360deg);
  }

  to {
    transform: rotate(0deg);
  }
}
