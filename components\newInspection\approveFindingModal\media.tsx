import React, { useState, useEffect } from "react";
import Image from "next/image";
import { Tooltip } from "antd";

const Media = ({
    isUpdate,
    UpdateInspection,
    handleViewUpdateInspectionMedia,
    media,
    handleViewMedia,
    setUrlsToDelete,
    urlsToDelete,
    setUpdateInspection,
    setMedia,
    setImagesArray,
    setVideosArray,
    imagesArray,
    videosArray
}: any) => {

  return (
    <>
      {isUpdate &&
        UpdateInspection?.media.map((item: any, index: number) => {
          return (
            <div
              className="w-[90px] h-[90px] rounded-[15px] flex justify-center items-center cursor-pointer relative"
              key={index}
              onClick={() =>
                handleViewUpdateInspectionMedia(item.url, item.isVideo)
              }
            >
              <Tooltip title="Delete" placement="bottom"> 
                <Image
                  src="/images/home/<USER>"
                  width={24}
                  height={24}
                  alt="image"
                  className="w-[24px] h-[24px] object-cover absolute top-2 right-2 z-10 bg-white rounded-lg"
                  onClick={(e) => {
                    e.stopPropagation();
                    
                    const mediaIndex = UpdateInspection.media.findIndex((mediaItem: any) => mediaItem.url === item.url);
                    
                    // const resizedMediaUrl = UpdateInspection.resize_media?.[mediaIndex]?.url;
                    
                    setUrlsToDelete([
                      ...urlsToDelete,
                      {
                        isVideo: item.isVideo,
                        mediaIndex: mediaIndex,
                        url: item.url,
                        resize_url: item.resize_url,
                      }
                    ]);

                    const updatedMedia = UpdateInspection.media.filter(
                      (mediaItem: any, index: number) => index !== mediaIndex
                    );
                    
                    // const updatedResizedMedia = UpdateInspection.resize_media?.filter(
                    //   (_: any, index: number) => index !== mediaIndex
                    // );

                    setUpdateInspection({
                      ...UpdateInspection,
                      media: updatedMedia,
                      // resize_media: updatedResizedMedia
                    });
                  }}
                />
              </Tooltip>
              {!item.isVideo ? (
                <img
                  src={item.url}
                  width={150}
                  height={150}
                  alt="image"
                  className="w-full h-full rounded-[15px] border object-cover"
                  onError={(e) => {
                    e.currentTarget.onerror = null; // Prevents infinite loops
                    e.currentTarget.src =
                      "/images/newInspection/fallbackImage.png";
                    e.currentTarget.className =
                      "w-full h-full rounded-[15px] border object-cover";
                  }}
                />
              ) : (
                <div className="w-full h-full border rounded-[15px] relative">
                  <video className="w-full h-full rounded-[15px] object-cover">
                    <source src={item.url} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                  <div className="flex justify-center items-center bg-[#12121257] w-full h-full rounded-[15px] absolute bottom-0">
                    <Image
                      src={"/images/newFindings/Vector.png"}
                      width={30}
                      height={32}
                      alt="play"
                      className="w-[30px] h-[30px]"
                    />
                  </div>
                </div>
              )}
            </div>
          );
        })}
      {media &&
        media.map((item: any, index: number) => {
          return (
            <div
              className="w-[90px] h-[90px] rounded-[15px] flex justify-center items-center object-cover cursor-pointer relative"
              key={index}
              onClick={() => handleViewMedia(item.src, item.type)}
            >
              <Tooltip title="Delete" placement="bottom"> 
                <Image
                  src="/images/home/<USER>"
                  width={24}
                  height={24}
                  alt="image"
                  className="w-[24px] h-[24px] object-cover rounded-lg absolute top-2 right-2 z-10 bg-white"
                  onClick={async (e) => {
                    e.stopPropagation();
                    const currentSrc = item.src;
                    
                    // Remove from media array
                    setMedia(media.filter((mediaItem: any) => mediaItem.src !== currentSrc));
                    
                    // Helper function to convert File to base64
                    const fileToBase64 = (file: File): Promise<string> => {
                      return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.readAsDataURL(file);
                        reader.onload = () => resolve(reader.result as string);
                        reader.onerror = (error) => reject(error);
                      });
                    };
                    
                    // Find and remove the actual file from the respective array
                    if (item.type === "image") {
                      const newImagesArray = [...imagesArray];
                      for (let i = 0; i < newImagesArray.length; i++) {
                        const base64 = await fileToBase64(newImagesArray[i]);
                        if (base64 === currentSrc) {
                          newImagesArray.splice(i, 1);
                          break;
                        }
                      }
                      setImagesArray(newImagesArray);
                    } else {
                      const newVideosArray = [...videosArray];
                      for (let i = 0; i < newVideosArray.length; i++) {
                        const base64 = await fileToBase64(newVideosArray[i]);
                        if (base64 === currentSrc) {
                          newVideosArray.splice(i, 1);
                          break;
                        }
                      }
                      setVideosArray(newVideosArray);
                    }
                  }}
                />
              </Tooltip>
              {item.type === "image" ? (
                <Image
                  src={item.src}
                  width={150}
                  height={150}
                  alt="image"
                  className="w-full h-full rounded-[15px] border object-cover"
                />
              ) : (
                <div className="w-full h-full border rounded-[15px] relative">
                  <video className="w-full h-full rounded-[15px] object-cover">
                    <source src={item.src} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                  <div className="flex justify-center items-center bg-[#12121257] w-full h-full rounded-[15px] absolute bottom-0">
                    <Image
                      src={"/images/newFindings/Vector.png"}
                      width={30}
                      height={32}
                      alt="play"
                      className="w-[30px] h-[30px]"
                    />
                  </div>
                </div>
              )}
            </div>
          );
        })}
    </>
  );
};

export default Media;
