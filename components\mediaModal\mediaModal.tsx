import React, { useEffect, useState } from "react";
import { Modal, Spin } from "antd";
import { LoadingOutlined } from '@ant-design/icons';

interface MediaOverlayProps {
  showMedia: boolean;
  setShowMedia: React.Dispatch<React.SetStateAction<boolean>>;
  mediaType: string;
  source: string;
}

const ViewMedia: React.FC<MediaOverlayProps> = ({
  showMedia,
  setShowMedia,
  mediaType,
  source
}) => {
  const [loading, setLoading] = useState(true);

  const handleLoad = () => {
    setLoading(false);
  };

  const handleError = () => {
    setLoading(false);
    // Optionally handle error state
  };

  useEffect(() => {
    if (source) {
      setLoading(true);
    }
  }, [source]);

  return (
    <Modal
      centered
      open={showMedia}
      onCancel={() => {
        setShowMedia(false);
      }}
      footer={null}
      width="60%"
      style={{ padding: 0 }}
    >
      <div className="flex justify-center items-center h-[80vh] relative">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Spin indicator={<LoadingOutlined spin />} size="large" />
          </div>
        )}
        
        {mediaType === "img" ? (
          <img 
            src={source} 
            alt="Preview" 
            className={`max-w-full h-[80vh] object-contain ${loading ? 'opacity-0' : 'opacity-100'}`}
            onLoad={handleLoad}
            onError={handleError}
          />
        ) : (
          <video 
            src={source} 
            controls 
            className={`max-w-full h-[80vh] ${loading ? 'opacity-0' : 'opacity-100'}`}
            onLoadedData={handleLoad}
            onError={handleError}
          />
        )}
      </div>
    </Modal>
  );
};

export default ViewMedia;