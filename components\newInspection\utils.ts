import React, { useCallback, useEffect, useState } from "react";
import { handleUpload, updateFindingDetails } from "@/src/services/newInspection.api";
import imageCompression from "browser-image-compression";
import { handleUploadWhileEdit } from "@/src/services/newFindings.api";

// Types for the scroll hook
interface UseHorizontalScrollOptions {
  scrollAmount?: number;
  scrollSpeed?: number;
  isDraggingDND?: boolean;
}

// Custom hook for horizontal scrolling with keyboard and mouse
export const useHorizontalScroll = (
  scrollContainerRef: React.RefObject<HTMLDivElement>,
  options: UseHorizontalScrollOptions = {}
) => {
  const {
    scrollAmount = 10,
    scrollSpeed = 100,
    isDraggingDND = false
  } = options;

  const [isDraggingScroll, setIsDraggingScroll] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Handle keyboard arrow keys for scrolling
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;

    if (e.key === "ArrowLeft") {
      container.scrollLeft -= scrollAmount;
      e.preventDefault();
    } else if (e.key === "ArrowRight") {
      container.scrollLeft += scrollAmount;
      e.preventDefault();
    }
  }, [scrollAmount, scrollContainerRef]);

  // Handle mouse down to start drag scrolling
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!scrollContainerRef.current || isDraggingDND) return;

    // Improved check to allow dragging from more areas
    // This allows dragging from any part of the container or its direct children
    const container = scrollContainerRef.current;
    const target = e.target as Node;

    // Check if the target is the container or a descendant of the container
    if (container.contains(target)) {
      // Prevent drag start on interactive elements like buttons, inputs, dropboxes, etc.
      // Also prevent on DND draggable elements
      const isInteractiveElement =
        (e.target as HTMLElement).tagName === 'BUTTON' ||
        (e.target as HTMLElement).tagName === 'INPUT' ||
        (e.target as HTMLElement).tagName === 'TEXTAREA' ||
        (e.target as HTMLElement).tagName === 'SELECT' ||
        (e.target as HTMLElement).tagName === 'A' ||
        (e.target as HTMLElement).closest('.ant-btn') !== null ||
        (e.target as HTMLElement).closest('[data-dropbox="true"]') !== null ||
        (e.target as HTMLElement).closest('[data-rbd-draggable-context-id]') !== null ||
        (e.target as HTMLElement).closest('[data-rbd-drag-handle-context-id]') !== null ||
        (e.target as HTMLElement).hasAttribute('data-rbd-drag-handle-draggable-id') ||
        (e.target as HTMLElement).closest('[data-rbd-drag-handle-draggable-id]') !== null;

      if (!isInteractiveElement) {
        // Set drag state immediately for instant response
        setIsDraggingScroll(true);

        // Calculate initial position immediately
        const initialX = e.pageX - container.offsetLeft;
        const initialScrollLeft = container.scrollLeft;

        setStartX(initialX);
        setScrollLeft(initialScrollLeft);

        // Change cursor to indicate grabbing immediately
        document.body.style.cursor = 'grabbing';
        container.style.cursor = 'grabbing';

        // Prevent text selection and other default behaviors
        e.preventDefault();
        e.stopPropagation();
      }
    }
  }, [scrollContainerRef, isDraggingDND]);

  // Handle mouse move to perform drag scrolling
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDraggingScroll || !scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const x = e.pageX - container.offsetLeft;
    const walk = (x - startX) * scrollSpeed; // Adjust scrolling speed with multiplier

    // Apply scroll immediately without requestAnimationFrame for instant response
    // Use the initial scrollLeft position to maintain consistent drag behavior
    container.scrollLeft = scrollLeft - walk;

    e.preventDefault();
  }, [isDraggingScroll, scrollSpeed, startX, scrollLeft, scrollContainerRef]);

  // Handle mouse up to end drag scrolling
  const handleMouseUp = useCallback(() => {
    setIsDraggingScroll(false);
    document.body.style.cursor = 'default';

    // Reset container cursor as well
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.cursor = 'default';
    }
  }, [scrollContainerRef]);

  // Set up event listeners
  useEffect(() => {
    // Add keyboard navigation handlers
    window.addEventListener("keydown", handleKeyDown);

    // Add mouse handlers for drag scrolling
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseleave', handleMouseUp);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mouseleave', handleMouseUp);
    };
  }, [handleKeyDown, handleMouseMove, handleMouseUp]);

  return { handleMouseDown };
};

export const generateDecompTitle = (array: any, object: any) => {
  try {

    // Ensure the array has at least one element
    if (array.length < 1) {
      return "Decomposition item";
    }

    // Parse the object if it's a string
    const fields = typeof object === "string" ? JSON.parse(object) : object;

    // Find the objects for the specified fields
    const titleFields = array.map((id: any) =>
      fields.find((field: any) => field?.id === id)
    );

    // If only one field is provided
    if (titleFields.length === 1) {
      const titleField = titleFields[0];
      if (!titleField) {
        return "Decomposition item";
      }

      // Handle nested options structure for the single field
      if (
        titleField?.options &&
        typeof titleField?.options === "object" &&
        !Array.isArray(titleField?.options)
      ) {
        // If options is an object, we get the nested array based on the suggested_value
        const nestedOptions =
          titleField?.options[titleField?.suggested_value];
        return (
          nestedOptions?.find(
            (opt: any) => opt?.id === titleField?.suggested_value
          )?.value || "Decomposition Item"
        );
      } else {
        // Regular structure for the single field
        return (
          titleField?.options.find(
            (opt: any) => opt?.id === titleField.suggested_value
          )?.value || "Decomposition Item"
        );
      }
    }

    // If two fields are provided
    const title1Field = titleFields[0];
    const title2Field = titleFields[1];

    if (!title1Field || !title2Field) {
      return "Decomposition item";
    }

    // Handle title1Field options (if it's nested as an object with arrays)
    let title1;
    if (
      title1Field?.options &&
      typeof title1Field?.options === "object" &&
      !Array.isArray(title1Field?.options)
    ) {
      // Handle nested structure for title1Field
      const nestedOptions =
        title1Field?.options[title1Field?.suggested_value];
      title1 = nestedOptions?.find(
        (opt: any) => opt?.id === title1Field?.suggested_value
      )?.value;
    } else {
      // Regular structure for title1Field
      title1 = title1Field?.options.find(
        (opt: any) => opt?.id === title1Field?.suggested_value
      )?.value;

      return title1
    }

    // Handle title2Field options (if it's nested as an object with arrays)
    // let title2;
    // if (
    //   title2Field?.options &&
    //   typeof title2Field?.options === "object" &&
    //   !Array.isArray(title2Field?.options)
    // ) {
    //   // Handle nested structure for title2Field
    //   const parentKey = title1Field?.suggested_value; // We use title1's suggested_value to get the nested options
    //   const nestedOptions = title2Field?.options[parentKey];
    //   title2 = nestedOptions?.find(
    //     (opt: any) => opt?.id === title2Field?.suggested_value
    //   )?.value;
    // } else {
    //   // Regular structure for title2Field
    //   title2 = title2Field?.options.find(
    //     (opt: any) => opt?.id === title2Field?.suggested_value
    //   )?.value;
    // }

    // return title1 && title2 ? `${title1} - ${title2}` : "Decomposition Item";
    return title1 ? title1 : "Decomposition Item12";
  } catch (error) {
    console.error("Error generating title:", error);
    return "Decomposition item";
  }
};

export function generateFindingTitle(
  array: any,
  object: any,
  index: number,
  parent: any
) {

  // Ensure the array has at least one element
  try {
    // console.log('array', array)
    // console.log('object', JSON.parse(object))
    // console.log('parent', parent)
    // console.log('index', index)
    if (array.length < 1) {
      return "Finding";
    }
    // Parse the object if it's a string
    const fields = typeof object === "string" ? JSON.parse(object) : object;

    // Find the field by name
    const currentField = fields.find(
      (field: any) => field?.id === array[index]
    );

    if (!currentField) {
      // console.log("Field not found:", array[index]);
      return "Finding";
    }

    // If options is an array, handle direct options
    if (Array.isArray(currentField?.options)) {
      const value = currentField?.options?.find(
        (opt: any) => opt?.id === currentField?.suggested_value
      )?.value;
      return value || "Finding";
    }

    // If options is an object, handle nested options
    if (currentField?.options && typeof currentField?.options === "object") {
      // Find the parent field
      const parentField = fields.find(
        (field: any) => field?.id === currentField?.parent_field
      );

      if (!parentField) {
        const parentKey = JSON.parse(parent?.fields || "[]").find(
          (item: any) => item?.id === currentField?.parent_field
        )?.suggested_value;

        // console.log('parentKey', parentKey)

        // Get the nested options array using the parent key
        const nestedOptions = currentField?.options[parentKey];
        if (!nestedOptions) {
          // console.log("No nested options found for key:", parentKey);
          return "Finding";
        }

        // Find the value in nested options
        const value = nestedOptions.find(
          (opt: any) => opt?.id === currentField?.suggested_value
        )?.value || nestedOptions[0]?.value;
        // console.log('currentField', currentField)
        // console.log('currentField?.suggested_value', currentField?.suggested_value)
        // console.log('nestedOptions', nestedOptions)
        // console.log('value', value)
        // console.log('===============================================')
        // console.log('5555', 5555)
        return value || "Finding";
      }

      // Get the parent's selected value
      const parentKey = parentField.suggested_value;

      // Get the nested options array using the parent key
      const nestedOptions = currentField?.options[parentKey];
      if (!nestedOptions) {
        // console.log("No nested options found for key:", parentKey);
        return "Finding";
      }

      // Find the value in nested options
      const value = nestedOptions.find(
        (opt: any) => opt?.id === currentField?.suggested_value
      )?.value;
      return value || "Finding";
    }

    if (currentField?.type === "number" || currentField?.type === "text" || currentField?.type === "date_picker") {
      const suggestedValue = currentField?.suggested_value;
      if (suggestedValue) {
        return (typeof suggestedValue !== 'string') ? JSON.stringify(suggestedValue) : suggestedValue;
      }
    }

    return "Finding";
  } catch (error) {
    console.error("Error generating title:", error);
    return "Finding";
  }
}

export const getMultiPickerSuggestedValue = (field: any) => {
  const { options, suggested_value } = field;

  // Handle flat options array
  if (options && Array.isArray(options)) {
    const option = options.filter((opt) => suggested_value.includes(opt.id)).map((i) => i.value);
    return option ? option.join(", ") : "-";
  }

  return "-"; // Suggested value not found
}

export function generateMeasureTitle(
  array: any,
  object: any,
  index: number,
  parent: any
) {
  // Ensure the array has at least one element
  try {
    if (array.length < 1) {
      return "Measure";
    }
    // Parse the object if it's a string
    const fields = typeof object === "string" ? JSON.parse(object) : object;

    // Find the field by name
    const currentField = fields.find(
      (field: any) => field?.id === array[index]
    );

    if (!currentField) {
      // console.log("Field not found:", array[index]);
      return "Measure";
    }

    // If options is an array, handle direct options
    if (Array.isArray(currentField?.options)) {
      const value = currentField?.options?.find(
        (opt: any) => opt?.id === currentField?.suggested_value
      )?.value;
      return value || "Measure";
    }

    // If options is an object, handle nested options
    if (currentField?.options && typeof currentField?.options === "object") {
      // Find the parent field
      const parentField = fields.find(
        (field: any) => field?.id === currentField?.parent_field
      );
      if (!parentField) {
        const parentKey = JSON.parse(parent?.fields || "[]").find(
          (item: any) => item?.id === currentField?.parent_field
        )?.suggested_value;

        // Get the nested options array using the parent key
        const nestedOptions = currentField?.options[parentKey];
        if (!nestedOptions) {
          // console.log("No nested options found for key:", parentKey);
          return "Measure";
        }

        // Find the value in nested options
        const value = nestedOptions.find(
          (opt: any) => opt?.id === currentField?.suggested_value
        )?.value;
        return value || "Measure";
      }

      // Get the parent's selected value
      const parentKey = parentField.suggested_value;

      // Get the nested options array using the parent key
      const nestedOptions = currentField?.options[parentKey];
      if (!nestedOptions) {
        // console.log("No nested options found for key:", parentKey);
        return "Measure";
      }

      // Find the value in nested options
      const value = nestedOptions.find(
        (opt: any) => opt?.id === currentField?.suggested_value
      )?.value;
      return value || "Measure";
    }

    if (currentField?.type === "number" || currentField?.type === "text" || currentField?.type === "date_picker") {
      const suggestedValue = currentField?.suggested_value;
      if (suggestedValue) {
        return (typeof suggestedValue !== 'string') ? JSON.stringify(suggestedValue) : suggestedValue;
      }
    }

    return "Measure";
  } catch (error) {
    console.error("Error generating title:", error);
    return "Measure";
  }
}

export const handleInpectionDetails = (router: any, inspectionName: string) => {
  const inspectionId = localStorage.getItem("ScalarInspectionId");
  localStorage.setItem("ViewInspectionDetails", "true");
  router.push(`/newInspection/inspectionMetaData?prev=newInspection&inspection=${inspectionName}`);
};

export const createfieldvaluesObject = (arr: any) => {
  const obj: any = {};

  arr.forEach((item: any) => {
    if (item.id && item.suggested_value !== undefined) {
      obj[item.name] = item.suggested_value;
    }
  });

  return obj;
};

export const handleMoreFileDrop = async (
    event: React.DragEvent,
    findingId: string,
    existingMedia: any,
    setLoadingIds: React.Dispatch<React.SetStateAction<string[]>>,
    fetchFindings: any
  ) => {
    try {
      setLoadingIds((prev: any) => [...prev, findingId]);
      const files = Array.from(event.dataTransfer.files);

      const mediaFiles = files.filter(
        (file) =>
          file.type.startsWith("image/") || file.type.startsWith("video/")
      );
      if (mediaFiles.length > 0) {
        const newMedia: any = await handleUploadWhileEdit(mediaFiles);
        if (newMedia.length > 0) {
          // Combine existing media with new media
          const allMedia = [...existingMedia, ...newMedia];

          // Update finding with all media URLs
          const mediaDetails = {
            media: allMedia,
          };
          await updateFindingDetails(mediaDetails, findingId);
          await fetchFindings();
        }
      }
    } catch (error) {
      console.error("error", error);
    } finally {
      setLoadingIds((prev: any) =>
        prev.filter((id: string) => id !== findingId)
      );
    }
  };