export const res = {
    "fields": [
        {
            "id": "test_field",
            "name": "test_field",
            "options": [
                {
                    "id": "test_option",
                    "value": "test_option"
                },
                {
                    "id": "test_option2",
                    "value": "test_option2"
                }
            ],
            "parent_field": null,
            "required": true,
            "suggested_value": null,
            "type": "picker"
        },
        {
            "name": "<PERSON><PERSON>ie",
            "options": [],
            "parent_field": null,
            "required": false,
            "suggested_value": null,
            "type": "location"
        }
    ],
    "inspection_type_id": "structural",
    "project": null
}