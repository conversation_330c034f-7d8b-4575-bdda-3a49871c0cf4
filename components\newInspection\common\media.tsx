import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { Tooltip, Spin } from "antd";
import { LoadingOutlined } from '@ant-design/icons';

interface MediaProps {
  // Main media configurations
  isUpdate?: boolean;
  UpdateInspection?: any;
  media?: any[];
  isEditMode?: boolean;
  
  // Event handlers
  handleViewUpdateInspectionMedia?: (url: string, isVideo: boolean) => void;
  handleViewMedia?: (src: string, type: string) => void;
  
  // State setters for media management
  setUrlsToDelete?: React.Dispatch<React.SetStateAction<any[]>>;
  urlsToDelete?: any[];
  setUpdateInspection?: React.Dispatch<React.SetStateAction<any>>;
  setMedia?: React.Dispatch<React.SetStateAction<any[]>>;
  
  // Arrays for images and videos
  setImagesArray?: React.Dispatch<React.SetStateAction<File[]>>;
  setVideosArray?: React.Dispatch<React.SetStateAction<File[]>>;
  imagesArray?: File[];
  videosArray?: File[];
}

const Media: React.FC<MediaProps> = ({
  isUpdate = false,
  UpdateInspection,
  handleViewUpdateInspectionMedia = () => {},
  media = [],
  handleViewMedia = () => {},
  setUrlsToDelete = () => {},
  urlsToDelete = [],
  setUpdateInspection = () => {},
  setMedia = () => {},
  setImagesArray = () => {},
  setVideosArray = () => {},
  imagesArray = [],
  videosArray = [],
  isEditMode = false,
}) => {
  // Track loading state for each media item
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
  const existingMediaRefMap = useRef<Map<string, boolean>>(new Map());
  const newMediaRefMap = useRef<Map<string, boolean>>(new Map());
  
  // Helper function to generate a unique key for each media item
  const getMediaKey = (url: string, index: number, prefix: string = '') => 
    `${prefix}${url}-${index}`;

  // Handle existing media items (from UpdateInspection)
  useEffect(() => {
    if (!isUpdate || !UpdateInspection?.media) return;

    // Reset the map when the media changes
    existingMediaRefMap.current = new Map();
    
    // Initialize loading states for existing media
    const newLoadingStates: Record<string, boolean> = {...loadingStates};
    
    UpdateInspection.media.forEach((item: any, index: number) => {
      const mediaUrl = item.url || item.src;
      const mediaKey = getMediaKey(mediaUrl, index, 'existing-');
      
      // Only set loading to true if we haven't already tracked this item
      if (!existingMediaRefMap.current.has(mediaKey)) {
        newLoadingStates[mediaKey] = true;
        existingMediaRefMap.current.set(mediaKey, true);
      }
    });
    
    setLoadingStates(newLoadingStates);
  }, [isUpdate, UpdateInspection?.media]);

  // Handle new media items
  useEffect(() => {
    if (!media || media.length === 0) return;
    
    // Reset the map when the media changes
    newMediaRefMap.current = new Map();
    
    // Initialize loading states for new media
    const newLoadingStates: Record<string, boolean> = {...loadingStates};
    
    media.forEach((item: any, index: number) => {
      const mediaUrl = item.src || item.url;
      const mediaKey = getMediaKey(mediaUrl, index, 'new-');
      
      // Only set loading to true if we haven't already tracked this item
      if (!newMediaRefMap.current.has(mediaKey)) {
        newLoadingStates[mediaKey] = true;
        newMediaRefMap.current.set(mediaKey, true);
      }
    });
    
    setLoadingStates(newLoadingStates);
  }, [media]);

  // Handler for when media loads
  const handleMediaLoaded = (key: string) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: false
    }));
  };

  // Handler for media load errors
  const handleMediaError = (key: string) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: false
    }));
  };

  // Helper function to convert File to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  };

  // Check if image is already cached by the browser
  const checkIfImageIsLoaded = (src: string, mediaKey: string) => {
    const img = document.createElement('img'); // Create a new HTMLImageElement
    img.onload = () => handleMediaLoaded(mediaKey);
    img.onerror = () => handleMediaError(mediaKey);
    img.src = src;
    
    // If the image is already in browser cache, onload may fire immediately
    if (img.complete) {
      handleMediaLoaded(mediaKey);
    }
  };

  // Preload videos to check if they're cached
  const checkIfVideoIsLoaded = (src: string, mediaKey: string) => {
    const video = document.createElement('video');
    
    // Add event listeners
    video.onloadeddata = () => handleMediaLoaded(mediaKey);
    video.onerror = () => handleMediaError(mediaKey);
    
    // Set source and attempt to load
    video.src = src;
    video.load();
    
    // If the video is ready before we even attached the event listener
    if (video.readyState >= 3) { // HAVE_FUTURE_DATA or higher
      handleMediaLoaded(mediaKey);
    }
  };

  // Preload existing media to check their loading state
  useEffect(() => {
    if (isUpdate && UpdateInspection?.media) {
      UpdateInspection.media.forEach((item: any, index: number) => {
        const mediaUrl = item.url || item.src;
        const mediaKey = getMediaKey(mediaUrl, index, 'existing-');
        const isVideoMedia = item.isVideo || (item.type === 'video');
        
        if (isVideoMedia) {
          checkIfVideoIsLoaded(mediaUrl, mediaKey);
        } else {
          checkIfImageIsLoaded(mediaUrl, mediaKey);
        }
      });
    }
  }, [isUpdate, UpdateInspection?.media]);

  // Preload new media
  useEffect(() => {
    if (media && media.length > 0) {
      media.forEach((item: any, index: number) => {
        const mediaUrl = item.src || item.url;
        const mediaKey = getMediaKey(mediaUrl, index, 'new-');
        const isVideoMedia = item.type === 'video' || item.isVideo;
        
        if (isVideoMedia) {
          checkIfVideoIsLoaded(mediaUrl, mediaKey);
        } else {
          checkIfImageIsLoaded(mediaUrl, mediaKey);
        }
      });
    }
  }, [media]);

  return (
    <>
      {/* Handle media from UpdateInspection (existing media) */}
      {isUpdate &&
        UpdateInspection?.media?.map((item: any, index: number) => {
          const mediaUrl = item.url || item.src;
          const mediaKey = getMediaKey(mediaUrl, index, 'existing-');
          const isLoading = loadingStates[mediaKey];
          const isVideoMedia = item.isVideo || (item.type === 'video');

          return (
            <div
              className="w-[90px] h-[90px] rounded-[15px] flex justify-center items-center cursor-pointer relative"
              key={mediaKey}
              onClick={() => handleViewUpdateInspectionMedia(mediaUrl, isVideoMedia)}
            >
              {isEditMode && (
                <Tooltip title="Delete" placement="bottom">
                  <Image
                    src="/images/home/<USER>"
                    width={24}
                    height={24}
                    alt="image"
                    className="w-[24px] h-[24px] object-cover absolute top-2 right-2 z-10 bg-white rounded-lg"
                    onClick={(e) => {
                      e.stopPropagation();

                      const mediaIndex = UpdateInspection.media.findIndex(
                        (mediaItem: any) => (mediaItem.url || mediaItem.src) === mediaUrl
                      );

                      setUrlsToDelete([
                        ...urlsToDelete,
                        {
                          isVideo: isVideoMedia,
                          mediaIndex: mediaIndex,
                          url: mediaUrl,
                          resize_url: item.resize_url,
                        },
                      ]);

                      const updatedMedia = UpdateInspection.media.filter(
                        (_: any, idx: number) => idx !== mediaIndex
                      );

                      setUpdateInspection({
                        ...UpdateInspection,
                        media: updatedMedia,
                      });
                    }}
                  />
                </Tooltip>
              )}
              
              {/* Show loader while image is loading */}
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50 rounded-[15px] z-10">
                  <Spin indicator={<LoadingOutlined spin />} />
                </div>
              )}
              
              {!isVideoMedia ? (
                <img
                  src={mediaUrl}
                  width={150}
                  height={150}
                  alt="image"
                  className={`w-full h-full rounded-[15px] border object-cover ${isLoading ? 'opacity-50' : 'opacity-100'}`}
                  onLoad={() => handleMediaLoaded(mediaKey)}
                  onError={(e) => {
                    handleMediaError(mediaKey);
                    e.currentTarget.onerror = null; // Prevents infinite loops
                    e.currentTarget.src = "/images/newInspection/fallbackImage.png";
                    e.currentTarget.className = "w-full h-full rounded-[15px] border object-cover";
                  }}
                />
              ) : (
                <div className="w-full h-full border rounded-[15px] relative">
                  <video 
                    className={`w-full h-full rounded-[15px] object-cover ${isLoading ? 'opacity-50' : 'opacity-100'}`}
                    onLoadedData={() => handleMediaLoaded(mediaKey)}
                    onError={() => handleMediaError(mediaKey)}
                  >
                    <source src={mediaUrl} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                  <div className="flex justify-center items-center bg-[#12121257] w-full h-full rounded-[15px] absolute bottom-0">
                    <Image
                      src={"/images/newFindings/Vector.png"}
                      width={30}
                      height={32}
                      alt="play"
                      className="w-[30px] h-[30px]"
                    />
                  </div>
                </div>
              )}
            </div>
          );
        })}

      {/* Handle newly added media items */}
      {media?.map((item: any, index: number) => {
        const mediaUrl = item.src || item.url;
        const mediaKey = getMediaKey(mediaUrl, index, 'new-');
        const isLoading = loadingStates[mediaKey];
        const mediaType = item.type || (item.isVideo ? 'video' : 'image');
        
        return (
          <div
            className="w-[90px] h-[90px] rounded-[15px] flex justify-center items-center object-cover cursor-pointer relative"
            key={mediaKey}
            onClick={() => handleViewMedia(mediaUrl, mediaType)}
          >
            <Tooltip title="Delete" placement="bottom">
              <Image
                src="/images/home/<USER>"
                width={24}
                height={24}
                alt="image"
                className="w-[24px] h-[24px] object-cover rounded-lg absolute top-2 right-2 z-10 bg-white"
                onClick={async (e) => {
                  e.stopPropagation();
                  const currentSrc = mediaUrl;

                  // Remove from media array
                  setMedia(
                    media.filter(
                      (mediaItem: any) => (mediaItem.src || mediaItem.url) !== currentSrc
                    )
                  );

                  // Find and remove the actual file from the respective array
                  if (mediaType === "image") {
                    const newImagesArray = [...imagesArray];
                    for (let i = 0; i < newImagesArray.length; i++) {
                      try {
                        const base64 = await fileToBase64(newImagesArray[i]);
                        if (base64 === currentSrc) {
                          newImagesArray.splice(i, 1);
                          break;
                        }
                      } catch (err) {
                        console.error("Error converting file to base64:", err);
                      }
                    }
                    setImagesArray(newImagesArray);
                  } else {
                    const newVideosArray = [...videosArray];
                    for (let i = 0; i < newVideosArray.length; i++) {
                      try {
                        const base64 = await fileToBase64(newVideosArray[i]);
                        if (base64 === currentSrc) {
                          newVideosArray.splice(i, 1);
                          break;
                        }
                      } catch (err) {
                        console.error("Error converting file to base64:", err);
                      }
                    }
                    setVideosArray(newVideosArray);
                  }
                }}
              />
            </Tooltip>
            
            {/* Show loader while media is loading */}
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50 rounded-[15px] z-10">
                <Spin indicator={<LoadingOutlined spin />} />
              </div>
            )}
            
            {mediaType === "image" ? (
              <Image
                src={mediaUrl}
                width={150}
                height={150}
                alt="image"
                className={`w-full h-full rounded-[15px] border object-cover ${isLoading ? 'opacity-50' : 'opacity-100'}`}
                onLoad={() => handleMediaLoaded(mediaKey)}
                onError={() => handleMediaError(mediaKey)}
              />
            ) : (
              <div className="w-full h-full border rounded-[15px] relative">
                <video 
                  className={`w-full h-full rounded-[15px] object-cover ${isLoading ? 'opacity-50' : 'opacity-100'}`}
                  onLoadedData={() => handleMediaLoaded(mediaKey)}
                  onError={() => handleMediaError(mediaKey)}
                >
                  <source src={mediaUrl} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="flex justify-center items-center bg-[#12121257] w-full h-full rounded-[15px] absolute bottom-0">
                  <Image
                    src={"/images/newFindings/Vector.png"}
                    width={30}
                    height={32}
                    alt="play"
                    className="w-[30px] h-[30px]"
                  />
                </div>
              </div>
            )}
          </div>
        );
      })}
    </>
  );
};

export default Media;