'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Poppins } from 'next/font/google';
import { Button, Modal as AntdModal, message } from 'antd';
import { CompleteInspections } from '@/src/services/newInspection.api';

const poppins = Poppins({ weight: '400', subsets: ['latin'] });

interface MarkCompleteModalProps {
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
  setMenu: (open: boolean) => void;
  selectedInspections: string[];
  setRefresh: (cb: (prev: any) => any) => void;
  setSearchedInspections: (inspections: any[]) => void;
  searchedInspections: any[];
}

const MarkCompleteModal: React.FC<MarkCompleteModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  selectedInspections,
  setRefresh,
  setSearchedInspections,
  searchedInspections,
  setMenu
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const markCompleteInspections = async () => {
    try {
      setLoading(true);
      if (selectedInspections.length > 0) {
        const res = await CompleteInspections(selectedInspections);

        if (res) {
          const updatedInspections = searchedInspections.map((insp: any) =>
            selectedInspections.includes(insp.id)
              ? { ...insp, isCompleted: true }
              : insp
          );

          setSearchedInspections(updatedInspections);
          message.success(t('Successfully completed all inspections.'));
        } else {
          message.warning(t('Some inspections failed to complete.'));
        }
      }
    } catch (error) {
      message.error(t('Something went wrong, try again later!'));
    } finally {
      setLoading(false);
      setIsModalOpen(false);
      setMenu(false)
    }
  };

  return (
    <AntdModal
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      centered
      className={`${poppins.className} custom-complete-modal`}
      width={350}
    >
      <div className="relative">

        <div className="mt-6">
          <h1 className="text-left text-[25px] leading-[52.08px] font-[600]">
            {t('Complete')} {t('Inspections')}!
          </h1>
          <p className="text-[18px] text-left mt-2">
            {t('Are you sure you want to mark')} <br />
            {t('Inspections')} {t('as complete')}?
          </p>

          <div className="text-center flex justify-between gap-4 mt-10">
            <Button
              type="default"
              className="w-[50%] h-[45px] text-[14px] text-[#FF9200] border border-[#FF9200] bg-[#ff910020] hover:bg-[#ff910015]"
              onClick={() => setIsModalOpen(false)}
            >
              {t('Cancel')}
            </Button>
            <Button
              type="primary"
              className="w-[50%] h-[45px] text-[14px] border-none"
              style={{
                backgroundColor: '#FF9200',
                color: 'white',
              }}
              onClick={markCompleteInspections}
              loading={loading}
            >
              {t('Complete')}
            </Button>
          </div>
        </div>
      </div>
    </AntdModal>
  );
};

export default MarkCompleteModal;
