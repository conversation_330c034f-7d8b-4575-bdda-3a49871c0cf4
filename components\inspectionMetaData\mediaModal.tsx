import { Modal } from "antd";
import React from "react";

const ImageModal = ({ showMedia, setShowMedia, imageUrl }: any) => {
  return (
    <Modal
      centered
      open={showMedia}
      onOk={() => setShowMedia(false)}
      onCancel={() => setShowMedia(false)}
      footer={null}
      className="custom-modal" // Apply custom class
    >
      <img
        src={imageUrl}
        alt="Advertisement"
        className="w-full h-[60vh] object-contain rounded-[10px]"
      />
    </Modal>
  );
};

export default ImageModal;
