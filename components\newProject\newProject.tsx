"use client";
import React, { useEffect, useState } from "react";
import Navbar from "@/components/Navbar/navbar";
import Sidebar from "@/components/sidebar/sidebar";
import { Roboto, Poppins, Open_Sans } from "next/font/google";
import { Button, Input, Select, Upload, UploadProps, message } from "antd";
import { useRouter } from "next/navigation";
import { useLoaderContext } from "@/context/LoaderContext";
import Loader from "@/components/loader/loader";
import DeleteModal from "@/components/deleteModal/deleteModal";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import Radio from "@mui/material/Radio";
import {
  fetchUser,
  getAllTeamMembers,
  addProjectDetail,
} from "@/src/services/project.api";
import { getUserReportTypes } from "@/src/services/auth.api";
import { InboxOutlined, RightOutlined } from "@ant-design/icons";
import { Timestamp } from "firebase/firestore";
import { useSidebarContext } from "@/context/sidebarContext";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const { Dragger } = Upload;
const { TextArea } = Input;

const NewProject = () => {
  const { isCollapsed } = useSidebarContext();
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [searchInspection, setSearchInspection] = useState<any>("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  const [refresh, setRefresh] = useState(false);
  const [inspectionTypeList, setInspectionTypeList] = useState<any>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isMemberDropdownOpen, setIsMemberDropdownOpen] = useState(false);
  const [inspectionType, setInspectionType] = useState(null);
  const [teamMembers, setTeamMembers] = useState<any>([]);
  const [selectedTeamMembers, setSelectedTeamMembers] = useState<any>([]);
  const [uploadedFile, setUploadedFile] = useState<any>(null);

  const router = useRouter();
  const { t } = useTranslation();

  const { loader, setLoader } = useLoaderContext();

  const fetchTeamMembers = async (firebaseUid: any) => {
    try {
      const userDetails: any = await fetchUser(firebaseUid);
      if (userDetails) {
        const res = await getAllTeamMembers(userDetails.client);
        const members = convertObjectsToValueLabel(res);
        setTeamMembers(members);
      } else {
        message.error(t("Something went wrong!"));
      }
    } catch (error) {
      message.error(t("Error fetching team members."));
    }
  };

  const fetchIsnspectionTypes = async (firebaseUid: any) => {
    try {
      const res: any = await getUserReportTypes(firebaseUid);
      if (res.inspectionTypes) {
        setInspectionTypeList(res.inspectionTypes);
      } else {
        message.error(t("Something went wrong!"));
      }
    } catch (error) {
      message.error(t("Error fetching Inspection types."));
    }
  };

  const fetchTeamMembersAndInspectionTypes = async (firebaseUid: string) => {
    try {
      setLoader(true);
      await fetchTeamMembers(firebaseUid);
      await fetchIsnspectionTypes(firebaseUid);
    } catch (error) {
      message.error(t("Something went wrong!"));
    } finally {
      setLoader(false);
    }
  };

  function convertObjectsToValueLabel(objectArray: any) {
    return objectArray
      .filter((obj: any) => obj.name)
      .map((obj: any, index: any) => {
        // const emailParts = obj.email.split("@");

        return {
          value: obj?.id,
          label: obj?.name,
        };
      });
  }

  useEffect(() => {
    const laguage: any = localStorage.getItem("ScalarLanguage");
    handleChangeLanguage(laguage);
    localStorage.removeItem("ViewInspectionDetails");
    const firebaseUid: any = localStorage.getItem("ScalarFirebaseUid");
    fetchTeamMembersAndInspectionTypes(firebaseUid);
  }, [refresh]);

  //   const handleDelete = async (e: any) => {
  //     e.preventDefault();

  //     const inspectionDetails: any = {
  //       is_deleted: true,
  //     };
  //     setLoader(true);
  //     const res: any = await deleteInspection(inspectionDetails, deleteId);
  //     if (res) {
  //       message.success(t("Successfully deleted!"));
  //       setLoader(false);
  //       setIsModalOpen(false);
  //       setRefresh(!refresh);
  //     } else {
  //       setLoader(false);
  //       setIsModalOpen(false);
  //       setRefresh(!refresh);
  //       message.error(t("Something went wrong, try again later!"));
  //     }
  //   };

  const handleTeamMemberSelection = (value: string[]) => {
    setSelectedTeamMembers(value);
  };

  const props: UploadProps = {
    name: "file",
    multiple: false,
    accept: ".xlsx,application/zip",
    onChange(info) {
      const { status } = info.file;
      if (status !== "uploading") {
      }
      if (status === "done") {
        message.success(`${info.file.name} file uploaded successfully.`);
        setUploadedFile(info.file); // Store the uploaded file in state

        // Redirect to another page after the file is uploaded
        // router.push("/your-next-page");
      } else if (status === "error") {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    onDrop(e) {
      // console.log("Dropped files", e.dataTransfer.files);
    },
  };

  const handleSaveProject = async () => {
    try {
      if (projectName.trim() === "" && inspectionType === null) {
        message.error(t("Name and inspection type can not be empty."));
        return;
      }
      if (projectName.trim() === "") {
        message.error(t("Name can not be empty!"));
        return;
      }
      if (inspectionType === null) {
        message.error(t("Please select an inspection type!"));
        return;
      }
      setLoader(true);
      const userId: any = localStorage.getItem("ScalarUserId");
      const clientId: any = localStorage.getItem("client");
      const timeStamp = Timestamp.now();
      const projectDetails = {
        name: projectName,
        client: clientId,
        description: projectDescription,
        inspections: [],
        created_at: timeStamp,
        updated_at: timeStamp,
      };
      const res = await addProjectDetail(
        projectDetails,
        inspectionType,
        userId,
        selectedTeamMembers
      );
      if (res) {
        router.back();
        message.success(t("Project created successfully"))!;
      }
    } catch (error) {
      message.error(t("Something went wrong!"));
    } finally {
      setLoader(false);
    }
  };

  return (
    <>
      {loader && <Loader />}
      <div className="flex h-screen">
        <Sidebar />
        <div
          className={`${
            isCollapsed ? "w-[calc(100vw-60px)]" : "w-[calc(100vw-16%)]"
          }`}
        >
          <Navbar
            search={false}
            searchInspection={searchInspection}
            setSearch={setSearchInspection}
          />
          <div
            className={`h-[calc(100%-60px)] text-black ${poppins.className} overflow-auto scrollbar pb-10`}
          >
            <div className="w-full bg-white flex justify-between items-center px-10 pt-6 pb-4 mb-2 sticky top-0 z-10">
              <h1 className="flex gap-1 text-[24px] leading-[24px] font-[500]">
                <p
                  className="hover:underline cursor-pointer transition-all"
                  onClick={() => router.push("/home")}
                >
                  {t("Projects")}
                </p>{" "}
                <RightOutlined className="text-[18px]" /> {t("New Project")}
              </h1>
              <div className="flex gap-2">
                <Button
                  onClick={handleSaveProject}
                  type="primary"
                  className={`${poppins.className} w-[100px] custom-button px-4 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px] `}
                >
                  <p className={`text-white text-[14px] font-[400]`}>
                    {t("Save")}
                  </p>
                </Button>
                <Button
                  onClick={() => router.back()}
                  type="primary"
                  className={`${poppins.className} w-[100px] custom-button px-4 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px] `}
                >
                  <p className={`text-white text-[14px] font-[400]`}>
                    {t("Cancel")}
                  </p>
                </Button>
              </div>
            </div>
            <div className="px-10 flex flex-col gap-6">
              {/* Project Name */}
              <div className="flex flex-col">
                <label className={`mb-2 font-semibold ${OpenSans.className}`}>
                  {t("Project Name")}
                </label>
                <Input
                  type="text"
                  placeholder={t("Enter name")}
                  className={`w-[70%] h-[55px] shadow-sm border-[#B4B4B4] border-[1px] rounded-[10px] border-opacity-[50%]`}
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                />
              </div>

              {/* Inspection type list */}
              <div className="w-[70%] flex flex-col relative">
                <label className={`mb-2 font-semibold ${OpenSans.className}`}>
                  {t("Inspection Type")}
                </label>
                {isDropdownOpen && (
                  <div className="bg-white w-[25px] h-[25px] rounded-[50%] absolute top-[50px] right-[38px] z-20 cursor-pointer"></div>
                )}
                <Select
                  allowClear
                  style={{ width: "100%", height: "55px" }}
                  placeholder={t("Select Inspection Type")}
                  defaultValue={inspectionType}
                  onChange={(value) => setInspectionType(value)}
                  options={inspectionTypeList}
                  onDropdownVisibleChange={(open) => setIsDropdownOpen(open)}
                  suffixIcon={
                    <img
                      src={
                        isDropdownOpen
                          ? "/images/newInspection/arrow-up.svg"
                          : "/images/newInspection/arrow-down.svg"
                      }
                      alt="dropdown icon"
                    />
                  }
                  filterOption={(input, option) =>
                    typeof option?.label === "string" &&
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                />
              </div>

              {/* Team Member list */}
              <div className="w-[70%] flex flex-col relative">
                <label className={`mb-2 font-semibold ${OpenSans.className}`}>
                  {t("Team members")}
                </label>
                <Select
                  mode="multiple"
                  className="w-full teamMember-selector min-h-[55px]"
                  allowClear
                  placeholder={t("Select Team Members")}
                  defaultValue={selectedTeamMembers}
                  onChange={handleTeamMemberSelection}
                  options={teamMembers}
                  onDropdownVisibleChange={(open) =>
                    setIsMemberDropdownOpen(open)
                  }
                  suffixIcon={
                    <img
                      src={
                        isMemberDropdownOpen
                          ? "/images/newInspection/arrow-up.svg"
                          : "/images/newInspection/arrow-down.svg"
                      }
                      alt="dropdown icon"
                    />
                  }
                  filterOption={(input, option) =>
                    typeof option?.label === "string" &&
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                />
              </div>

              {/* Project Description */}
              <div className="flex flex-col">
                <label className={`mb-2 font-semibold ${OpenSans.className}`}>
                  {t("Project Description")}
                </label>
                <TextArea
                  placeholder={t("Enter description")}
                  autoSize={{ minRows: 1, maxRows: 6 }}
                  className={`w-[70%] py-4 shadow-sm border-[#B4B4B4] border-[1px] rounded-[10px] border-opacity-[50%]`}
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                />
              </div>

              {/* Xlsx file uploader */}
              {/* <div className="flex flex-col">
                <label className={`mb-2 font-semibold ${OpenSans.className}`}>
                  {t("Import object file")}
                </label>
                <Dragger {...props}>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">
                    Choose a file or drag it hear
                  </p>
                  <p className="ant-upload-hint">(.xlsx or shape file (zip))</p>
                </Dragger>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default NewProject;
