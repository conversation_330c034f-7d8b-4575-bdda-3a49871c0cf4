"use client";
import React, { useState, useEffect } from "react";
import { DragDropContext, Draggable } from "@hello-pangea/dnd";
import StrictModeDroppable from "./StrictModeDroppable";
import { Poppins } from "next/font/google";
import { useTranslation } from "react-i18next";
import DecompositionRow from "./decompRow";
import EditDecompModal from "./editDecompModal/editModal";
import EditMeasureModal from "./editMeasureModal/editMeasureModal";
import Media from "@/components/mediaModal/mediaModal";
import { updateFinfingOrder } from "@/src/services/newInspection.api";
import { useFindingContext } from "@/context/findingContext";


const poppins = Poppins({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

interface FindingOverviewTableProps {
  decompositionGroups: any[];
  setDecompositionGroups: React.Dispatch<React.SetStateAction<any[]>>;
  orderArray: any[];
  setOrderArray: React.Dispatch<React.SetStateAction<any[]>>;
  setRefresh: React.Dispatch<React.SetStateAction<boolean>>;
  groupedFindings: any[];
  setGroupedFindings: React.Dispatch<React.SetStateAction<any[]>>;
}

const FindingOverviewTable: React.FC<FindingOverviewTableProps> = ({
  decompositionGroups,
  setDecompositionGroups,
  orderArray,
  setOrderArray,
  setRefresh,
  groupedFindings,
  setGroupedFindings,
}) => {
  const { t } = useTranslation();
  const [isDecompModalOpen, setIsDecompModalOpen] = useState(false);
  // const [isMeasureModalOpen, setIsMeasureModalOpen] = useState(false)
  const [isMeasureModalOpen, setIsMeasureModalOpen] = useState(false);
  const [selectedDecompId, setSelectedDecompId] = useState<string | null>(null);
  const [selectedMeasure, setSelectedMeasure] = useState<string | null>(
    null
  );
  const [showMedia, setShowMedia] = useState(false);
  const [mediaSource, setMediaSource] = useState("");
  const [mediaType, setMediaType] = useState("");
  const [maxFieldCount, setMaxFieldCount] = useState(0);
  const { setWholeGroup } = useFindingContext();
  const [fieldNames, setFieldNames] = useState<string[]>([]);

  useEffect(() => {
    if (groupedFindings && groupedFindings.length > 0) {
      let maxFields = 0;
      const allFieldNames: Set<string> = new Set();
      const fieldNameArray: string[] = [];

      decompositionGroups.forEach((group) => {
        if (group.measures && group.measures.length > 0) {
          group.measures.forEach((item: any) => {
            if (item.fields) {
              try {
                const fieldsObj =
                  typeof item.fields === "string"
                    ? JSON.parse(item.fields)
                    : item.fields;

                if (Array.isArray(fieldsObj)) {
                  const relevantFields = fieldsObj.filter(
                    (field: any) =>
                      field.type === "picker" ||
                      field.type === "multipicker" ||
                      field.type === "text" ||
                      field.type === "number" ||
                      field.type === "parent_api" ||
                      field.type === "calculation" ||
                      field.type === "api"
                  );
                
                  // Collect all field names
                  relevantFields.forEach((field: any, index: number) => {
                    if (field.name && !item.title_fields.slice(0,1).includes(field.id)) { // Skip the first field as it becomes the title
                      allFieldNames.add(field.name);
                    }
                  });

                  maxFields = Math.max(maxFields, relevantFields.length);
                }
              } catch (e) {
                console.error("Error parsing fields:", e);
              }
            }
          });
        }
      });

      // Convert set to array and handle the first field separately
      allFieldNames.forEach(name => {
        fieldNameArray.push(name);
      });

      setFieldNames(fieldNameArray);
      setMaxFieldCount(fieldNameArray.length + 1);
    }
  }, [groupedFindings]);

  const handleDecompClick = (decompId: string) => {
    localStorage.setItem("findingId", decompId);
    localStorage.setItem("isFindingUpdate", "true");
    localStorage.setItem("isMeasureUpdate", "false");
    setSelectedDecompId(decompId);
    const currentGroup = groupedFindings.find((group) => group.id === decompId);
    setWholeGroup(currentGroup);
    sessionStorage.setItem("currentId", decompId);
    const id: any = process.env.NEXT_DECOMP_ID;
    sessionStorage.setItem("FindingTypeId", id);
    localStorage.setItem("isFindingUpdate", "true");
    localStorage.setItem("isMeasureUpdate", "false");
    localStorage.setItem("findingId", decompId);
    setIsDecompModalOpen(true);
  };

  const handleFindingClick = (measureId: string, parentId: string) => {
    const currentGroup = groupedFindings.find((group) => group.id === parentId);
    const measure = currentGroup.decompositionItem.find((item: any) => item.id === measureId)
    setSelectedMeasure(measure);
    setWholeGroup(currentGroup);
    setIsMeasureModalOpen(true);
  };

  const handleImageClick = (src: string, isVideo: boolean) => {
    setMediaSource(src);
    setMediaType(isVideo ? "video" : "img");
    setShowMedia(true);
  };

  const handleDragEnd = (result: any) => {
    const { destination, source, draggableId, type } = result;

    // Debug logging
    // console.log("DragEnd result:", {
    //   destination,
    //   source,
    //   draggableId,
    //   type,
    // });

    if (!destination) {
      // console.log("No destination, drag cancelled");
      return;
    }

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      // console.log("Dropped in same position, no changes needed");
      return;
    }

    if (type === "column") {
      // console.log("Handling decomposition drag");
      // Handle decomposition reordering
      const newGroups = Array.from(decompositionGroups);
      const [removed] = newGroups.splice(source.index, 1);
      newGroups.splice(destination.index, 0, removed);
      setDecompositionGroups(newGroups);

      // Build a completely new order array preserving the parent-child relationships
      const newOrderArray: any[] = [];

      // First, create a map of all findings and their measures for quick lookup
      const findingMeasuresMap: Record<string, any[]> = {};
      orderArray.forEach((item: any) => {
        if (item.type === "measure") {
          if (!findingMeasuresMap[item.parentId]) {
            findingMeasuresMap[item.parentId] = [];
          }
          findingMeasuresMap[item.parentId].push(item);
        }
      });

      // Process decompositions in their new order
      newGroups.forEach((group) => {
        // Add the decomposition itself
        newOrderArray.push({
          id: group.id,
          type: "decomposition",
          parentId: null,
        });

        // Add all findings for this decomposition
        const findingsForThisDecomp = orderArray.filter(
          (item) => item.type === "finding" && item.parentId === group.id
        );

        // Add each finding followed by its measures
        findingsForThisDecomp.forEach((finding) => {
          newOrderArray.push(finding);

          // Add all measures for this finding
          const measuresForThisFinding = findingMeasuresMap[finding.id] || [];
          newOrderArray.push(...measuresForThisFinding);
        });
      });

      setOrderArray(newOrderArray);
      const newOrderIds = newOrderArray.map((item: any) => item.id);
      localStorage.setItem("findingOrder", JSON.stringify(newOrderIds));
      updateFinfingOrder(newOrderIds);

      // Trigger refresh
      setRefresh((prev) => !prev);
    } else if (type === "task") {
      // console.log("Handling finding drag");
      // Handle finding reordering within same decomposition
      if (source.droppableId === destination.droppableId) {
        const group = decompositionGroups.find(
          (g) => g.id === source.droppableId
        );
        if (!group) return;

        const newFindings = Array.from(group.findings);
        const [removed] = newFindings.splice(source.index, 1);
        newFindings.splice(destination.index, 0, removed);

        const newGroups = decompositionGroups.map((g) =>
          g.id === source.droppableId ? { ...g, findings: newFindings } : g
        );
        setDecompositionGroups(newGroups);

        // Create a completely new order array while maintaining the relationship between findings and measures
        const newOrderArray: any[] = [];

        // Create a map of all findings and their measures for quick lookup
        const findingMeasuresMap: Record<string, any[]> = {};
        orderArray.forEach((item: any) => {
          if (item.type === "measure") {
            if (!findingMeasuresMap[item.parentId]) {
              findingMeasuresMap[item.parentId] = [];
            }
            findingMeasuresMap[item.parentId].push(item);
          }
        });

        // Identify all the decompositions
        const decomps = orderArray.filter(
          (item) => item.type === "decomposition"
        );

        // For each decomposition
        decomps.forEach((decomp) => {
          // Add the decomposition
          newOrderArray.push(decomp);

          if (decomp.id === source.droppableId) {
            // This is the decomposition where findings were reordered
            // Get all findings for this decomposition in their new order
            const findingsInNewOrder = newFindings
              .map((finding: any) => {
                return orderArray.find(
                  (item) => item.type === "finding" && item.id === finding.id
                );
              })
              .filter(Boolean) as any[];

            // Add each finding followed by its measures
            findingsInNewOrder.forEach((finding) => {
              newOrderArray.push(finding);

              // Add all measures for this finding
              const measuresForThisFinding =
                findingMeasuresMap[finding.id] || [];
              newOrderArray.push(...measuresForThisFinding);
            });
          } else {
            // For other decompositions, maintain the existing order
            const findingsForThisDecomp = orderArray.filter(
              (item) => item.type === "finding" && item.parentId === decomp.id
            );

            findingsForThisDecomp.forEach((finding) => {
              newOrderArray.push(finding);

              // Add all measures for this finding
              const measuresForThisFinding =
                findingMeasuresMap[finding.id] || [];
              newOrderArray.push(...measuresForThisFinding);
            });
          }
        });

        setOrderArray(newOrderArray);
        const newOrderIds = newOrderArray.map((item: any) => item.id);
        localStorage.setItem("findingOrder", JSON.stringify(newOrderIds));
        updateFinfingOrder(newOrderIds);

        // Trigger refresh
        setRefresh((prev) => !prev);
      }
    }
  };

  return (
    <div className="px-10 pb-10">
      <div
        className={`table-container text-[12px] w-full border rounded-lg ${poppins.className}`}
      >
        <div
          className={`table-header grid bg-[#2F80ED] text-white px-4 py-3 rounded-t-md text-center z-10 sticky top-0`}
          style={{
            gridTemplateColumns: `minmax(80px, 1fr) repeat(${
          maxFieldCount - 1
        }, minmax(20px, 1fr))`,
          }}
        >
          <div className="">{t("")}</div>

          {fieldNames.map((fieldName, i) => (
            <div key={i} className="text-left truncate px-4">{t(fieldName)}</div>
          ))}
        </div>

        <DragDropContext onDragEnd={handleDragEnd}>
          <StrictModeDroppable droppableId="main-droppable" type="column">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="table-body"
              >
                {decompositionGroups.map((group, index) => (
                  <Draggable
                    key={group.id}
                    draggableId={group.id}
                    index={index}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className={`text-[10px] ${
                          snapshot.isDragging
                            ? "bg-blue-50 border-2 border-blue-300 shadow-lg"
                            : ""
                        }`}
                        style={{
                          ...provided.draggableProps.style,
                          cursor: "grab",
                        }}
                      >
                        <DecompositionRow
                          group={group}
                          onDecompClick={handleDecompClick}
                          onFindingClick={handleFindingClick}
                          onImageClick={handleImageClick}
                          groupedFindings={groupedFindings}
                          maxFieldCount={maxFieldCount}
                          fieldNames={fieldNames}
                        />
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </StrictModeDroppable>
        </DragDropContext>
      </div>

      {/* Modals */}
      {isDecompModalOpen && selectedDecompId && (
        <EditDecompModal
          isModalOpen={isDecompModalOpen}
          setIsModalOpen={setIsDecompModalOpen}
          setAllGroups={setDecompositionGroups}
          allGroups={decompositionGroups}
          setOrderArray={setOrderArray}
          orderArray={orderArray}
          setRefresh={setRefresh}
        />
      )}

      {isMeasureModalOpen && selectedMeasure && (
        <EditMeasureModal
        isModalOpen={isMeasureModalOpen}
        setIsModalOpen={setIsMeasureModalOpen}
        isMeasureUpdate={true}
        setGroupedFindings={setGroupedFindings}
        selectedMeasure={selectedMeasure}
        setAllGroups={setDecompositionGroups}
        setOrderArray={setOrderArray}
          orderArray={orderArray}
          setRefresh={setRefresh}
      />
      )}

      {/* Media Overlay */}
        <Media
          showMedia={showMedia}
          setShowMedia={setShowMedia}
          mediaType={mediaType}
          source={mediaSource}
        />
    </div>
  );
};

export default FindingOverviewTable;
