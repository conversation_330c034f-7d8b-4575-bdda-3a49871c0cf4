/** @type {import('next').NextConfig} */

import { withSentryConfig } from '@sentry/nextjs';

const nextConfig = {
    images: {
        domains: ["firebasestorage.googleapis.com"], // Replace with your actual image server domain
      },
      env: {
        NEXT_GOOGLE_PLACE_API: process.env.NEXT_GOOGLE_PLACE_API,
        NEXT_HOST: process.env.NEXT_HOST,
        NEXT_DB: process.env.NEXT_DB,
        NEXT_DECOMP_ID: process.env.NEXT_DECOMP_ID,
        NEXT_FINDING_ID: process.env.NEXT_FINDING_ID,
        NEXT_MEASURE_ID: process.env.NEXT_MEASURE_ID,
    },
};

const sentryWebpackPluginOptions = {
  silent: true,
};

export default withSentryConfig(nextConfig, sentryWebpackPluginOptions);

