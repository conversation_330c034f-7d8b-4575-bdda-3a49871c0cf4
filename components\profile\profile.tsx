"use client";
import React, { useEffect, useState } from "react";
import Navbar from "@/components/Navbar/navbar";
import Sidebar from "@/components/sidebar/sidebar";
import { Roboto, Poppins, Open_Sans } from "next/font/google";
import Image from "next/image";
import { languageOptions } from "@/src/libs/constants";
import { Select, Avatar, Input, Modal, message, Button } from "antd";
import { fetchUserDetails } from "@/src/services/profile.api";
import { useLoaderContext } from "@/context/LoaderContext";
import Loader from "@/components/loader/loader";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import PrivacyModal from "@/components/profile/privacyModal";
import TermsModal from "@/components/profile/tncModal";
import { useSidebarContext } from "@/context/sidebarContext";

const { Option } = Select;

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Profile = () => {
  const { t } = useTranslation();
  const { loader, setLoader } = useLoaderContext();
  const { isCollapsed } = useSidebarContext();
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [isTermsModalVisible, setIsTermsModalVisible] = useState(false);
  const [isPrivacyModalVisible, setIsPrivacyModalVisible] = useState(false);
  const [searchInspection, setSearchInspection] = useState<any>("");
  const [userdetails, setUserdetails] = useState<any>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const fetchUser = async () => {
    setLoader(true);
    const name = localStorage.getItem("ScalarUserName");
    const email = localStorage.getItem("ScalarUserEmail");
    const user = {
      name: name,
      email: email,
    };
    setUserdetails(user);
    setLoader(false);
    // const user = await fetchUserDetails();
    // if (user) {
    //   setUserdetails(user);
    //   setLoader(false)
    // } else{
    //     setLoader(false)
        // message.error(t("Something went wrong!"))
    // }
  };

  useEffect(() => {
    fetchUser();
    const laguage: any = localStorage.getItem("ScalarLanguage");
    setSelectedLanguage(laguage);
    handleChangeLanguage(laguage);
  }, []);

  const handleTermsClick = () => {
    setIsTermsModalVisible(true);
  };

  const handlePrivacyClick = () => {
    setIsPrivacyModalVisible(true);
  };

  const handleTermsOk = () => {
    setIsTermsModalVisible(false);
    setIsPrivacyModalVisible(true);
  };

  const handleTermsCancel = () => {
    setIsTermsModalVisible(false);
  };

  const handlePrivacyOk = () => {
    setIsPrivacyModalVisible(false);
  };

  const handlePrivacyCancel = () => {
    setIsPrivacyModalVisible(false);
  };

  return (
    <>
      {loader && <Loader />}
      <div className="flex h-screen">
        <Sidebar />
        <div className={`${isCollapsed ? "w-[calc(100vw-60px)]" : "w-[calc(100vw-16%)]"}`}>
          <Navbar search={false} searchInspection={searchInspection} setSearch={setSearchInspection} />
          <div
            className={` h-[calc(100%-60px)] px-10 text-black ${poppins.className} overflow-auto scrollbar`}
          >
            <div className="w-full flex justify-between pt-6 mb-1">
              <h1 className="text-[24px] leading-[24px] font-[500] pt-2">
                {t("Profile")}
              </h1>
            </div>
            <div className="w-full flex items-start text-black mt-4">
              <div className="w-[25%]">
                <div className="flex justify-center mt-8">
                  <Avatar
                    style={{
                      backgroundColor: "#2F80ED",
                      // verticalAlign: "middle",
                      width: "150px",
                      height: "150px",
                      fontSize: "60px",
                    }}
                    size="large"
                    className={`text-[60px] leading-[20px] font-[700] ${roboto.className}`}
                  >
                    {userdetails?.name !== "null"
                      ? userdetails?.name?.slice(0, 1).toUpperCase()
                      : userdetails?.email?.slice(0, 1).toUpperCase()}
                  </Avatar>
                </div>
                <h1 className="text-[22px] mt-[10px] text-center">
                  {userdetails?.name !== "null" ? userdetails?.name : ""}
                </h1>
              </div>
              <div className="w-[75%] pl-16 items-center">
                <div className="justify-center flex-col mb-5">
                  <label
                    className={` text-[13px] text-[#121212] text-opacity-[40%] font-[400] ${OpenSans.className}`}
                  >
                    {t("Name")}
                  </label>
                  <div
                    className={`w-[450px] h-[55px] mt-2 pl-1 flex items-center justify-start rounded-[10px] border-[#B4B4B4] border-[1px] border-opacity-[50%] font-[400] ${OpenSans.className}`}
                    style={{ boxShadow: "0 0 5px 5px rgba(0, 0, 0, 0.04)" }}
                  >
                    <h1
                      className={`ml-[10px] text-[15px] font-[400] ${OpenSans.className}`}
                    >
                      {userdetails?.name !== "null" ? (
                        userdetails?.name
                      ) : (
                        <p className="text-[20px] ml-2">-</p>
                      )}
                    </h1>
                  </div>
                </div>
                <div className="flex flex-col mb-5">
                  <label
                    className={`mb-2 text-[13px] text-[#121212] text-opacity-[40%] font-[400] ${OpenSans.className}`}
                  >
                    {t("Email")}
                  </label>
                  <div
                    className={`w-[450px] h-[55px] pl-1 flex items-center justify-start rounded-[10px] border-[#B4B4B4] border-[1px] border-opacity-[50%] font-[400] ${OpenSans.className}`}
                    style={{ boxShadow: "0 0 5px 5px rgba(0, 0, 0, 0.04)" }}
                  >
                    <h1
                      className={`ml-[10px] text-[15px] font-[400] ${OpenSans.className}`}
                    >
                      {userdetails?.email ? (
                        userdetails?.email
                      ) : (
                        <p className="text-[20px] ml-2">-</p>
                      )}
                    </h1>
                  </div>
                </div>
                <div className="flex flex-col mb-5">
                  <label
                    className={`mb-2 text-[13px] text-[#121212] text-opacity-[40%] font-[400] ${OpenSans.className}`}
                  >
                    {t("Language")}
                  </label>
                  <Select
                    value={selectedLanguage}
                    onChange={(value) => setSelectedLanguage(value)}
                    placeholder="Select language"
                    className={`w-[450px] h-[55px] rounded-[10px] font-[400] ${OpenSans.className}`}
                    suffixIcon={
                      <img
                        src={
                          isDropdownOpen
                            ? "/images/newInspection/arrow-up.svg"
                            : "/images/newInspection/arrow-down.svg"
                        }
                        alt="dropdown icon"
                      />
                    }
                    onDropdownVisibleChange={(open) => setIsDropdownOpen(open)}
                    style={{ boxShadow: "0 0 5px 5px rgba(0, 0, 0, 0.04)" }}
                  >
                    {languageOptions.map((option) => (
                      <Option key={option.value} value={option.value}>
                        <p className="text-[15px] font-[400] pl-1">
                          {option.label}
                        </p>
                      </Option>
                    ))}
                  </Select>
                </div>
                <div>
                  <button
                    className="leading-[14px] w-[450px] text-black flex justify-between items-center gap-2 mt-8 mb-2 py-3 px-2 rounded-[10px]"
                    onClick={handleTermsClick}
                  >
                    <h1
                      className={`flex justify-start pl-2 text-[16px] font-[400] ${OpenSans.className}`}
                    >
                      {t("Terms and Conditions")}
                    </h1>
                    <img
                      width={22}
                      height={22}
                      alt="logo"
                      src="/images/profile/rightErow.svg"
                      className="w-[22px] h-[22px] flex justify-end"
                    />
                  </button>
                  <button
                    className="leading-[14px] w-[450px] text-black flex justify-between items-center gap-2 mb-8 py-3 px-2 rounded-[10px]"
                    onClick={handlePrivacyClick}
                  >
                    <h1
                      className={`flex justify-start pl-2 text-[16px] font-[400] ${OpenSans.className}`}
                    >
                      {t("Privacy Policy")}
                    </h1>
                    <img
                      width={22}
                      height={22}
                      alt="logo"
                      src="/images/profile/rightErow.svg"
                      className="w-[22px] h-[22px]"
                    />
                  </button>
                  <Button
                    onClick={() => {
                      handleChangeLanguage(selectedLanguage);
                    }}
                    type="primary"
                    className={`${poppins.className} custom-button w-[450px] rounded-xl text-[15px] border-[#B4B4B4] border-opacity-30 bg-[#2F80ED] text-white h-[50px]`}
                  >
                    <p className={`text-white text-[14px] font-[400]`}>
                      {t("Save")}
                    </p>
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <TermsModal isTermsModalVisible={isTermsModalVisible} handleTermsOk={handleTermsOk} handleTermsCancel={handleTermsCancel} t={t} />
          <PrivacyModal isPrivacyModalVisible={isPrivacyModalVisible} handlePrivacyOk={handlePrivacyOk} handlePrivacyCancel={handlePrivacyCancel} t={t} />
          {/* <Modal
            title={
              <div className={`text-center text-[22px] ${poppins.className}`}>
                {t("Terms and Conditions")}
              </div>
            }
            open={isTermsModalVisible} // Changed from open to visible
            onOk={handleTermsOk}
            onCancel={handleTermsCancel}
            width={680}
            centered
            footer={null}
            className="custom-modal"
            styles={{
              body: {
                maxHeight: "80vh",
                overflowY: "auto",
              },
            }}
          >
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur. Netus ullamcorp nisi at ut
              urna nibh praesent. Lorem ipsum dolor sit amet consectetur. Erat
              ipsum vitae nunc elit. Adipiscing convallis tortor sem egestas sed
              varius velit sed netus. Euismod turpis ultrices eu vitae varius ac
              varius. Varius dictum in at interdum pretium.Lorem ipsum dolor sit
              amet consectetur. Erat ipsum vitae nunc elit. Adipiscing convallis
              tortor sem egestas sed varius velit sed netus. Euismod turpis
              ultrices eu vitae varius ac varius. Varius dictum in at interdum
              pretium.
            </p>
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur. Erat ipsum vitae nunc
              elit. Adipiscing convallis tortor sem egestas sed varius velit sed
              netus. Euismod turpis ultrices eu vitae varius ac varius. Varius
              dictum in at interdum pretium.
            </p>
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur.Lorem ipsum dolor sit amet
              consectetur. Netus ullamcorp nisi at ut urna nibh praesent. Lorem
              ipsum dolor sit amet consectetur. Erat ipsum vitae nunc elit.
              Adipiscing convallis tortor sem egestas sed varius velit sed
              netus. Euismod turpis ultrices eu vitae varius ac varius. Varius
              dictum in at interdum pretium.Lorem ipsum dolor sit amet
              consectetur. Erat ipsum vitae nunc elit. Adipiscing convallis
              tortor sem egestas sed varius velit sed netus. Euismod turpis
              ultrices eu vitae varius ac varius. Varius dictum in at interdum
              pretium.
            </p>
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur.Lorem ipsum dolor sit amet
              consectetur. Netus ullamcorp nisi at ut urna nibh praesent. Lorem
              ipsum dolor sit amet consectetur.
            </p>
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur.Lorem ipsum dolor sit amet
              consectetur. Netus ullamcorp nisi at ut urna nibh praesent. Lorem
              ipsum dolor sit amet consectetur. Erat ipsum vitae nunc elit.
              Adipiscing convallis tortor sem egestas sed varius velit sed
              netus. Euismod turpis ultrices eu vitae varius ac varius. Varius
              dictum in at interdum pretium.Lorem ipsum dolor sit amet
              consectetur. Erat ipsum vitae nunc elit. Adipiscing convallis
              tortor sem egestas sed varius velit sed netus. Euismod turpis
              ultrices eu vitae varius ac varius. Varius dictum in at interdum
              pretium.
            </p>
            <p className={`text-[14px] text-justify ${poppins.className}`}>
              Erat ipsum vitae nunc elit. Adipiscing convallis tortor sem
              egestas sed varius velit sed netus. Euismod turpis ultrices eu
              vitae varius ac varius. Varius dictum in at interdum pretium.Lorem
              ipsum dolor sit amet consectetur. Erat ipsum vitae nunc elit.
              Adipiscing convallis tortor sem egestas sed varius velit sed
              netus. Euismod turpis ultrices eu vitae varius ac varius. Varius
              dictum in at interdum pretium.
            </p>
          </Modal>
          <Modal
            title={
              <div className={`text-center text-[22px] ${poppins.className}`}>
                {t("Privacy Policy")}
              </div>
            }
            open={isPrivacyModalVisible}
            onOk={handlePrivacyOk}
            onCancel={handlePrivacyCancel}
            width={680}
            centered
            footer={null}
            className="custom-modal"
            styles={{
              body: {
                maxHeight: "80vh",
                overflowY: "auto",
              },
            }}
          >
            <p className="text-[18px] mb-2 font-bold">
              Lorem ipsum dolor sit amet consectetur
            </p>
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur. Netus ullamcorp nisi at ut
              urna nibh praesent. Lorem ipsum dolor sit amet consectetur. Erat
              ipsum vitae nunc elit. Adipiscing convallis tortor sem egestas sed
              varius velit sed netus. Euismod turpis ultrices eu vitae varius ac
              varius. Varius dictum in at interdum pretium.Lorem ipsum dolor sit
              amet consectetur. Erat ipsum vitae nunc elit. Adipiscing convallis
              tortor sem egestas sed varius velit sed netus. Euismod turpis
              ultrices eu vitae varius ac varius. Varius dictum in at interdum
              pretium.
            </p>
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur. Erat ipsum vitae nunc
              elit. Adipiscing convallis tortor sem egestas sed varius velit sed
              netus. Euismod turpis ultrices eu vitae varius ac varius. Varius
              dictum in at interdum pretium.
            </p>
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur.Lorem ipsum dolor sit amet
              consectetur. Netus ullamcorp nisi at ut urna nibh praesent. Lorem
              ipsum dolor sit amet consectetur. Erat ipsum vitae nunc elit.
              Adipiscing convallis tortor sem egestas sed varius velit sed
              netus. Euismod turpis ultrices eu vitae varius ac varius. Varius
              dictum in at interdum pretium.Lorem ipsum dolor sit amet
              consectetur. Erat ipsum vitae nunc elit. Adipiscing convallis
              tortor sem egestas sed varius velit sed netus. Euismod turpis
              ultrices eu vitae varius ac varius. Varius dictum in at interdum
              pretium.
            </p>
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur.Lorem ipsum dolor sit amet
              consectetur. Netus ullamcorp nisi at ut urna nibh praesent. Lorem
              ipsum dolor sit amet consectetur.
            </p>
            <p className={`text-[14px] text-justify mb-4 ${poppins.className}`}>
              Lorem ipsum dolor sit amet consectetur.Lorem ipsum dolor sit amet
              consectetur. Netus ullamcorp nisi at ut urna nibh praesent. Lorem
              ipsum dolor sit amet consectetur. Erat ipsum vitae nunc elit.
              Adipiscing convallis tortor sem egestas sed varius velit sed
              netus. Euismod turpis ultrices eu vitae varius ac varius. Varius
              dictum in at interdum pretium.Lorem ipsum dolor sit amet
              consectetur. Erat ipsum vitae nunc elit. Adipiscing convallis
              tortor sem egestas sed varius velit sed netus. Euismod turpis
              ultrices eu vitae varius ac varius. Varius dictum in at interdum
              pretium.
            </p>
            <p className={`text-[14px] text-justify ${poppins.className}`}>
              Erat ipsum vitae nunc elit. Adipiscing convallis tortor sem
              egestas sed varius velit sed netus. Euismod turpis ultrices eu
              vitae varius ac varius. Varius dictum in at interdum pretium.Lorem
              ipsum dolor sit amet consectetur. Erat ipsum vitae nunc elit.
              Adipiscing convallis tortor sem egestas sed varius velit sed
              netus. Euismod turpis ultrices eu vitae varius ac varius. Varius
              dictum in at interdum pretium.
            </p>
          </Modal> */}
        </div>
      </div>
    </>
  );
};

export default Profile;
