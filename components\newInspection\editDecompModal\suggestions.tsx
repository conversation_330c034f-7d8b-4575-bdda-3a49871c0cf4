import React, { useEffect, useState, useRef } from "react";
import { Cascader, Input, Button, Tooltip, Skeleton, DatePicker, message, Image } from "antd";
import { fetchAPIResponse } from "@/src/services/newFindings.api";
// import { TextareaAutosize } from "@mui/material";
import { LoadingOutlined } from "@ant-design/icons";
import { Spin } from "antd";
import { useFindingContext } from "@/context/findingContext";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import moment from "moment";
const { TextArea } = Input;

const Suggestions = ({
  editableFindings,
  setEditableFindings,
  textareaRef,
  isAddingMeasure,
  isMeasureUpdate,
  isUpdate,
  suggestionResponse,
  setSuggestionResponse,
  textSuggestionLoader,
  getTextSuggestions,
  isEditMode,
  fieldImage,
  setFieldImage,
  imageUrl,
  setImageUrl
}: any) => {
  const router = useRouter();
  const { t } = useTranslation();
  const { wholeGroup } = useFindingContext();
  const [disabledField, setDisabledField] = useState<any>([]);
  const [apiLodaer, setApiLodaer] = useState(false);
  const [apiResponses, setApiResponses] = useState<{ [key: string]: any }>({});

  const hasEffectRun = useRef(false);
  const inputRef = useRef<any>(null);

  const handleImage = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const dataUrl = reader.result as string; // Convert the result to a string
        setImageUrl(dataUrl);
      };
      reader.onerror = () => {
        message.error(t("Failed to read the file. Please try again."));
      };
      reader.readAsDataURL(file); // Use readAsDataURL for image source
    }
    setFieldImage(file);
  };

  useEffect(() => {
    if (!hasEffectRun.current) {
      const group: any = sessionStorage.getItem("group");
      const parsedGroup = JSON.parse(group);
      const currentId: any = sessionStorage.getItem("currentId");
      const findingTypeId: any = sessionStorage.getItem("FindingTypeId");
      if (
        isUpdate &&
        (findingTypeId === "9B56Oe0fiPTABJriqKn9" ||
          findingTypeId === "83mi507Lv3BizK7bJ7Hg" ||
          findingTypeId === process.env.NEXT_DECOMP_ID ||
          findingTypeId === process.env.NEXT_FINDING_ID)
      ) {
        if (wholeGroup === null) {
          router.push("/newInspection");
        } else {
          findDisabledField(wholeGroup, currentId, findingTypeId);
        }
      }
      hasEffectRun.current = true;
    }
  }, []);

  const applyValueFromParentFinding = (group: any, findingTypeId: any) => {
    if (findingTypeId === "83mi507Lv3BizK7bJ7Hg" || findingTypeId === process.env.NEXT_FINDING_ID) {
      const parsedParentFields = JSON.parse(group.parent.fields);
      const suggestions = [...editableFindings];
      suggestions.forEach((field: any) => {
        if (field.parent_field) {
          const parentField = parsedParentFields.find(
            (f: any) => f.id === field.parent_field
          );
          const options = field.options[parentField.suggested_value];
          field.suggested_value = options[0].id;
          return field;
        } else {
          return field;
        }
      });
      setEditableFindings(suggestions);
    }
  };

  const disableFieldRecursively = (fieldId: any, disabledFieldArray: any[]) => {
    const treeField = editableFindings.find((f: any) => f.id === fieldId);
    if (treeField && treeField.parent_field) {
      const parentFieldId = treeField.parent_field;
      if (!disabledFieldArray.includes(parentFieldId)) {
        disabledFieldArray.push(parentFieldId);
        setDisabledField((prev: any) => [...prev, parentFieldId]);
        disableFieldRecursively(parentFieldId, disabledFieldArray); // Recursive call
      }
    }
  };

  const findDisabledField = (
    group: any,
    currentId: any,
    findingTypeId: any
  ) => {
    const processFields = (items: any[]) => {
      let disabledFieldArray: any[] = [];
      items.forEach((item: any) => {
        const parsedItem = JSON.parse(item.fields);
        parsedItem.forEach((field: any) => {
          if (
            field.parent_field &&
            parentFieldsArray.includes(field.parent_field)
          ) {
            setDisabledField((prev: any) => [...prev, field.parent_field]);
            disabledFieldArray.push(field.parent_field);
          }
        });
      });
      const parentField = editableFindings.filter((f: any) =>
        disabledFieldArray.includes(f.id)
      );
      parentField.forEach((field: any) => {
        disableFieldRecursively(field.id, disabledFieldArray);
      });
      if (isAddingMeasure || isMeasureUpdate) {
        // Perform initial calculation based on the provided suggested values
        calculateFieldValues();
      }
      // applyValueFromParentFinding(group, findingTypeId);
    };

    let parentFieldsArray: any[] = [];

    if (group.id === currentId && (findingTypeId === "9B56Oe0fiPTABJriqKn9" || findingTypeId === process.env.NEXT_DECOMP_ID)) {
      const parentFields = JSON.parse(group.parent.fields);
      parentFieldsArray = parentFields.map((field: any) => field.id);
      processFields(group.decompositionItem.slice(1));
    }

    if (findingTypeId === "83mi507Lv3BizK7bJ7Hg" || findingTypeId === process.env.NEXT_FINDING_ID) {
      const parent = group.decompositionItem.find(
        (item: any) => item.id === currentId
      );
      const filteredFindings = group.decompositionItem.filter(
        (item: any) => item.parent_finding_id === currentId
      );

      const parentFields = JSON.parse(parent.fields);
      parentFieldsArray = parentFields.map((field: any) => field.id);
      processFields(filteredFindings);
    }
  };

  // Function to calculate the value for fields with "type": "calculation"
  const calculateFieldValues = () => {
    const updatedFindings = [...editableFindings];
    updatedFindings.forEach((field: any, index: any) => {
      if (field.type === "calculation" && field.calculation) {
        const { fields: calculationFields, type } = field.calculation;
        if (type === "multiply") {
          const values = calculationFields.map((fieldName: string) => {
            const relatedField = updatedFindings.find(
              (f) => f.name === fieldName
            );
            return relatedField ? relatedField.suggested_value : 0;
          });
          const result = values.reduce(
            (acc: any, value: any) => acc * (value || 0),
            1
          );
          updatedFindings[index].suggested_value = result;
        }
      }
    });
    setEditableFindings(updatedFindings);
  };

  // Function for updating the suggested value of a field
  const handlePickerChange = (value: any, index: any, isParent = false) => {
    const updatedFindings = [...editableFindings];
    const changedField = updatedFindings[index];
    if (changedField.type === "date_picker") {
      const formattedValue = dayjs(value).format("DD/MM/YYYY");
      updatedFindings[index].suggested_value = formattedValue;
    } else {
      updatedFindings[index].suggested_value = value;
    }
    const apiField = updatedFindings.find((item: any) => item.type === "api");
    if (apiField?.parameters?.input_fields?.includes(changedField.id)) {
      fetchAPI();
    }

    // Recursive function to update child fields
    const updateChildFields = (parentId: any) => {
      const childField = updatedFindings.find(
        (f: any) => f.parent_field === parentId
      );
      if (childField) {
        if (childField?.type === "picker") {
          const childIndex = updatedFindings.findIndex(
            (f: any) => f.id === childField.id
          );

          // Get the parent field to access its value
          const parentField = updatedFindings.find(
            (f: any) => f.id === parentId
          );
          const parentValue = parentField.suggested_value;

          // Get child options based on parent's value
          const childOptions = childField.options[parentValue] || [];

          // Update the child's value to the first option in the new options
          const newChildValue =
            childOptions.length > 0 ? childOptions[0].id : "";
          updatedFindings[childIndex].suggested_value = newChildValue;
          if (apiField?.parameters?.input_fields?.includes(childField.id)) {
            fetchAPI();
          }
        }

        // Recursively update this child's children
        updateChildFields(childField.id);
      }
    };

    // Start the recursive update from the current field
    updateChildFields(updatedFindings[index].id);

    // If this is part of a calculation, trigger recalculation
    const relatedCalculation = editableFindings.find(
      (f: any) =>
        f.type === "calculation" &&
        f.calculation.fields.includes(updatedFindings[index].name)
    );
    if (relatedCalculation) {
      calculateFieldValues();
    }

    // Clear suggested_values of dependent fields
    const clearDependentFields = (changedFieldId: string) => {
      updatedFindings.forEach((field) => {
        if (
          field.recalculation_parents &&
          field.recalculation_parents.length > 0
        ) {
          const recalculationParentIds = field.recalculation_parents.map(
            (parent: any) => parent.id
          );
          if (recalculationParentIds.includes(changedFieldId)) {
            if (field.type === "api") {
              fetchAPI();
            } else if (field.type === "text" || field.type === "number") {
              const suggestionResObj = suggestionResponse;
              suggestionResObj.suggestions.fields = updatedFindings;
              getTextSuggestions(suggestionResObj);
            }
          }
        }
      });

      // New recursive function to clear child values
      const clearChildrenValues = (parentId: string) => {
        const children = updatedFindings.filter(
          (f: any) => f.parent_field === parentId
        );
        if (children.length > 0) {
          children.forEach((child: any) => {
            clearDependentFields(child.id); // Recursively clear values of this child's children
            clearChildrenValues(child.id);
          });
        }
      };

      // Start the recursive clearing from the changed field
      clearChildrenValues(changedFieldId);
    };

    // Call the function with the changed field's ID
    clearDependentFields(changedField.id);

    setEditableFindings(updatedFindings);
  };

  // NEW: Function for handling multipicker value changes
  const handleMultipickerChange = (optionId: string, index: number) => {
    const updatedFindings = [...editableFindings];
    const field = updatedFindings[index];

    // Ensure suggested_value is always an array for multipicker fields
    if (!Array.isArray(field.suggested_value)) {
      field.suggested_value = field.suggested_value
        ? [field.suggested_value]
        : [];
    }

    // Toggle selection: add or remove the option from the array
    if (field.suggested_value.includes(optionId)) {
      field.suggested_value = field.suggested_value.filter(
        (id: string) => id !== optionId
      );
    } else {
      field.suggested_value = [...field.suggested_value, optionId];
    }

    const apiField = updatedFindings.find((item: any) => item.type === "api");
    if (apiField?.parameters?.input_fields?.includes(field.id)) {
      fetchAPI();
    }

    // Handle dependent fields if necessary
    const clearDependentFields = (changedFieldId: string) => {
      updatedFindings.forEach((field) => {
        if (
          field.recalculation_parents &&
          field.recalculation_parents.length > 0
        ) {
          const recalculationParentIds = field.recalculation_parents.map(
            (parent: any) => parent.id
          );
          if (recalculationParentIds.includes(changedFieldId)) {
            if (field.type === "api") {
              fetchAPI();
            } else if (field.type === "text" || field.type === "number") {
              const suggestionResObj = suggestionResponse;
              suggestionResObj.suggestions.fields = updatedFindings;
              getTextSuggestions(suggestionResObj);
            }
          }
        }
      });
    };

    clearDependentFields(field.id);
    setEditableFindings(updatedFindings);
  };

  // Prepare Cascader or Chip options
  const getCascaderOptions = (field: any) => {
    if (field.parent_field) {
      const parentField = editableFindings.find(
        (f: any) => f.id === field.parent_field
      );

      if (parentField) {
        const parentValue = parentField?.suggested_value;
        if (field.options[parentValue]) {
          return field.options[parentValue].map((option: any) => ({
            value: option.id,
            label: option.value,
          }));
        } else {
          return [];
        }
      } else {
        if (isUpdate) {
          // const group = JSON.parse(sessionStorage.getItem("group") || "{}");
          const currentId = sessionStorage.getItem("currentId");
          const currentfinding = wholeGroup.decompositionItem.find(
            (item: any) => item.id === currentId
          );
          const parentFinding = wholeGroup.decompositionItem.find(
            (item: any) => item.id === currentfinding?.parent_finding_id
          );
          const parentSuggestedValue = JSON.parse(
            parentFinding?.fields || "[]"
          ).find(
            (item: any) => item.id === field.parent_field
          )?.suggested_value;
          return (
            field.options[parentSuggestedValue]?.map((option: any) => ({
              value: option.id,
              label: option.value,
            })) || []
          );
        } else {
          // const group = JSON.parse(sessionStorage.getItem("group") || "{}");
          const parentId = sessionStorage.getItem("parent_finding_id");
          if (parentId) {
            const parentFinding = wholeGroup.decompositionItem.find(
              (item: any) => item.id === parentId
            );
            const parentSuggestedValue = JSON.parse(
              parentFinding?.fields || "[]"
            ).find(
              (item: any) => item.id === field.parent_field
            )?.suggested_value;
            return (
              field.options[parentSuggestedValue]?.map((option: any) => ({
                value: option.id,
                label: option.value,
              })) || []
            );
          }
        }
      }
    } else {
      return field?.options?.map((option: any) => ({
        value: option.id,
        label: option.value,
      }));
    }
  };

  // Handle chip button click
  const handleChipClick = (optionId: any, index: any) => {
    handlePickerChange(optionId, index);
  };

  // Handle Cascader selection
  const handleCascaderChange = (value: any, index: any) => {
    handlePickerChange(value[0], index);
  };

  // NEW: Handle Cascader selection for multipicker
  const handleMultipickerCascaderChange = (value: any, index: any) => {
    if (value && value.length > 0) {
      handleMultipickerChange(value[0], index);
    }
  };

  // NEW: Function to get visible options for multipicker
  const getVisibleMultipickerOptions = (field: any, options: any[]) => {
    if (!Array.isArray(field.suggested_value)) {
      field.suggested_value = field.suggested_value
        ? [field.suggested_value]
        : [];
    }

    // If we have 5 or fewer options total, show all options
    if (options.length <= 5) {
      return options;
    }

    // If selected options are 5 or more, show all selected options
    if (field.suggested_value.length >= 5) {
      return options.filter((option) =>
        field.suggested_value.includes(option.value)
      );
    }

    // Otherwise, show all selected options plus top options to make 5 total
    const selectedOptions = options.filter((option) =>
      field.suggested_value.includes(option.value)
    );

    const unselectedOptions = options.filter(
      (option) => !field.suggested_value.includes(option.value)
    );

    // Calculate how many unselected options we need to show
    const remainingSlots = 5 - selectedOptions.length;
    const topUnselectedOptions = unselectedOptions.slice(0, remainingSlots);

    // Combine and return
    return [...selectedOptions, ...topUnselectedOptions];
  };

  // Get remaining options for dropdown (those not shown as chips)
  const getRemainingOptions = (allOptions: any[], visibleOptions: any[]) => {
    const visibleOptionIds = visibleOptions.map((opt) => opt.value);
    return allOptions.filter((opt) => !visibleOptionIds.includes(opt.value));
  };

  const getAPIResponse = async (api_fields: any) => {
    try {
      setApiLodaer(true);
      const reportType = localStorage.getItem("reportTypeId");
      const inspection_id = localStorage.getItem("ScalarInspectionId");
      const findingTypeId = sessionStorage.getItem("FindingTypeId");
      const input_fields = editableFindings
        .filter((field: any) => api_fields.includes(field.id))
        .map((field: any) => {
          return {
            field_id: field.id,
            value_id: field.suggested_value,
            type: field.type,
          };
          if (field.type === "picker") {
            let value = "";
            if (field.parent_field) {
              const parentFieldValue = editableFindings.find(
                (f: any) => f.id === field.parent_field
              )?.suggested_value;
              if (parentFieldValue) {
                value =
                  field?.options[parentFieldValue]?.find(
                    (option: any) => option.id === field.suggested_value
                  )?.value || "";
              }
            } else {
              value =
                field?.options.find(
                  (option: any) => option.id === field.suggested_value
                )?.value || "";
            }
            return {
              field_id: field.id,
              value_id: field.suggested_value,
              // value: value,
              type: field.type,
            };
          } else {
            return {
              field_id: field.id,
              value_id: field.suggested_value,
              type: field.type,
            };
          }
        })
        .filter((field: any) => field !== undefined);
      const payload = {
        input_fields: input_fields,
      };
      const api_response = await fetchAPIResponse(
        reportType,
        inspection_id,
        findingTypeId,
        payload
      );
      return api_response;
    } catch (error) {
      console.error("Error in getAPIResponse:", error);
      return "";
    } finally {
      setApiLodaer(false);
    }
  };

  // Move API calls to useEffect
  const fetchAPI = async () => {
    const apiFields = editableFindings.filter(
      (field: any) => field.type === "api"
    );

    for (const field of apiFields) {
      try {
        const response = await getAPIResponse(field?.parameters?.input_fields);
        setApiResponses((prev) => ({
          ...prev,
          [field.id]: response,
        }));
      } catch (error) {
        console.error("Error fetching API response:", error);
      }
    }

    // After all API calls are complete, update editableFindings
    const updatedFindings = editableFindings.map((field: any) => {
      if (field.type === "api") {
        return {
          ...field,
          suggested_value: apiResponses[field.id]?.result?.value,
        };
      }
      return field;
    });

    setEditableFindings(updatedFindings);
  };

  return (
    <>
      {editableFindings.map((field: any, index: any) => {
        const isPicker =
          field.type === "picker" ||
          (field.options &&
            field.options.length > 0 &&
            field.type !== "multipicker");
        const isMultipicker = field.type === "multipicker";
        const isText = field.type === "text";
        const isNumber = field.type === "number";
        const isCalculation = field.type === "calculation";
        const isAPI = field.type === "api";
        const isParentAPI = field.type === "parent_api";
        const isDatePicker = field.type === "date_picker";
        const isImagePicker = field.type === "image_field";

        // Use the stored API response instead of calling in render
        if (isAPI) {
          field.suggested_value =
            apiResponses[field.id]?.result?.value || field.suggested_value;
        }

        // Get options for the field
        const options =
          isPicker ||
          isMultipicker ||
          (field.options && field.options.length > 0)
            ? getCascaderOptions(field)
            : [];

        // Make sure multipicker suggested_value is always an array
        if (isMultipicker && !Array.isArray(field.suggested_value)) {
          field.suggested_value = field.suggested_value ? [field.suggested_value] : [];
        }

        // For normal pickers
        if (isPicker && options && options.length > 0) {
          const value = options.find(
            (option: any) => option.value === field.suggested_value
          );
          if (!value && options.length > 0) {
            field.suggested_value = options[0].value;
          }
        }

        // Get visible options for multipicker fields
        const visibleMultipickerOptions = isMultipicker ?
          getVisibleMultipickerOptions(field, options) : [];

        // Get remaining options for dropdown
        const remainingMultipickerOptions = isMultipicker ?
          getRemainingOptions(options, visibleMultipickerOptions) : [];

        if (field?.conditional_field) {
          const parentValue = editableFindings.find((f: any) => f.id === field.parent_field)?.suggested_value;
          if (Array.isArray(parentValue)) {
            if (!parentValue.includes(field?.conditional_parent_field_id)) {
              return null; // Skip rendering this field if the condition is not met
            }
          }
          else if (field?.conditional_parent_field_id !== parentValue) {
            return null; // Skip rendering this field if the condition is not met
          }
        }

        return (
          <div
            key={index}
            className="bg-white w-full box-border grid grid-cols-7 gap-x-4 gap-y-0 items-center border-[#B4B4B44D]"
          >
            <div className="h-full col-span-1 flex flex-col justify-center text-[16px] leading-[16px] font-[500] text-left border-r-2">
              <h1 className="text-[14px]">
                {field.name}
                {field.required && isEditMode && (
                  <Tooltip
                    placement="right"
                    title="Required"
                    className="cursor-pointer"
                  >
                    <sup className="text-red-500">*</sup>
                  </Tooltip>
                )}
              </h1>
            </div>
            <div className={`col-span-6 py-3 ${index !== 0 && "border-t-2"}`}>
              {isPicker && (
                <div>
                  {isEditMode ? (
                    <div>
                      {options?.length <= 5 ? (
                        // Show chip buttons if options are <= 5
                        <div className="flex flex-wrap gap-2">
                          {options.map((option: any) => (
                            <Button
                              key={option.value}
                              shape="round"
                              disabled={disabledField.includes(field.id)}
                              className={`${
                                field.suggested_value === option.value
                                  ? "bg-blue-500 text-white"
                                  : "bg-white text-black"
                              } border min-w-[80px] h-[25px] text-[12px] p-0`}
                              style={
                                field.suggested_value === option.value
                                  ? {
                                      backgroundColor: "rgb(59 130 246)",
                                      color: "white",
                                    }
                                  : {}
                              }
                              onClick={() =>
                                field.suggested_value !== option.value &&
                                handleChipClick(option.value, index)
                              }
                            >
                              {option.label}
                            </Button>
                          ))}
                        </div>
                      ) : (
                        // Show first 5 chip buttons and use Cascader for the rest
                        <>
                          <div className="flex flex-wrap gap-2 mb-2">
                            {options?.slice(0, 5).map((option: any) => (
                              <Button
                                key={option.value}
                                shape="round"
                                disabled={disabledField.includes(field.id)}
                                className={`${
                                  field.suggested_value === option.value
                                    ? "bg-blue-500 text-white"
                                    : "bg-white text-black"
                                } border min-w-[80px] h-[25px] text-[12px] p-0`}
                                style={
                                  field.suggested_value === option.value
                                    ? {
                                        backgroundColor: "rgb(59 130 246)",
                                        color: "white",
                                      }
                                    : {}
                                }
                                onClick={() =>
                                  field.suggested_value !== option.value &&
                                  handleChipClick(option.value, index)
                                }
                              >
                                {option.label}
                              </Button>
                            ))}
                          </div>
                          <Cascader
                            options={options ? options : []}
                            onChange={(value: any) =>
                              handleCascaderChange(value, index)
                            }
                            allowClear={false}
                            disabled={disabledField.includes(field.id)}
                            // value={getValue(field.suggested_value, options)}
                            value={field.suggested_value}
                            showSearch
                            placeholder={t("Select an option")}
                            // placeholder={
                            //   options.find((option: any) => option.value === field.suggested_value)?.label || "Select an option"
                            // }
                            multiple={false}
                            className="mt-2 w-full h-[25px] text-[12px] p-0 placeholder-black"
                            dropdownRender={(menus) => (
                              <div
                                style={{
                                  maxWidth: "600px",
                                  maxHeight: "200px",
                                  overflowY: "auto",
                                }}
                                className="text-[12px]"
                              >
                                {menus}
                              </div>
                            )}
                          />
                        </>
                      )}
                    </div>
                  ) : (
                    // Read-only view for picker
                    <Button
                      shape="round"
                      disabled
                      className="bg-blue-500 text-white border min-w-[80px] h-[25px] text-[12px] p-0"
                      style={{
                        backgroundColor: "rgb(59 130 246)",
                        color: "white",
                      }}
                    >
                      {options?.find((opt: any) => opt.value === field.suggested_value)?.label || field.suggested_value}
                    </Button>
                  )}
                </div>
              )}

              {/* NEW: Multipicker field type */}
              {isMultipicker && (
                <div>
                  {isEditMode ? (
                    <div>
                      {/* Display visible options as chips */}
                      <div className="flex flex-wrap gap-2 mb-2">
                        {visibleMultipickerOptions.map((option: any) => (
                          <Button
                            key={option.value}
                            shape="round"
                            disabled={disabledField.includes(field.id)}
                            className={`${
                              Array.isArray(field.suggested_value) &&
                              field.suggested_value.includes(option.value)
                                ? "bg-blue-500 text-white"
                                : "bg-white text-black"
                            } border min-w-[80px] h-[25px] text-[12px] p-0`}
                            style={
                              Array.isArray(field.suggested_value) &&
                              field.suggested_value.includes(option.value)
                                ? {
                                    backgroundColor: "rgb(59 130 246)",
                                    color: "white",
                                  }
                                : {}
                            }
                            onClick={() =>
                              !disabledField.includes(field.id) &&
                              handleMultipickerChange(option.value, index)
                            }
                          >
                            {option.label}
                          </Button>
                        ))}
                      </div>

                      {/* Show dropdown for remaining options if any */}
                      {remainingMultipickerOptions.length > 0 && (
                        <Cascader
                          options={remainingMultipickerOptions || []}
                          onChange={(value: any) =>
                            handleMultipickerCascaderChange(value, index)
                          }
                          allowClear={false}
                          disabled={disabledField.includes(field.id)}
                          showSearch
                          value={[t("Select more options")]}
                          placeholder={t("Select more options")}
                          multiple={false}
                          className="my-2 w-full h-[25px] text-[12px] p-0 placeholder-black"
                          dropdownRender={(menus) => (
                            <div
                              style={{
                                maxWidth: "600px",
                                maxHeight: "200px",
                                overflowY: "auto",
                              }}
                            >
                              {menus}
                            </div>
                          )}
                        />
                      )}
                    </div>
                  ) : (
                    // Read-only view for multipicker
                    <div className="flex flex-wrap gap-2">
                      {options
                        ?.filter((opt: any) => field.suggested_value?.includes(opt.value))
                        .map((option: any) => (
                          <Button
                            key={option.value}
                            shape="round"
                            disabled
                            className="bg-blue-500 text-white border min-w-[80px] h-[25px] text-[12px] p-0"
                            style={{
                              backgroundColor: "rgb(59 130 246)",
                              color: "white",
                            }}
                          >
                            {option.label}
                          </Button>
                        ))}
                    </div>
                  )}
                </div>
              )}

              {isText && (
                <>
                  {textSuggestionLoader ? (
                    <div className="w-full flex justify-center">
                      <Skeleton.Input active block />
                    </div>
                  ) : (
                    <TextArea
                      ref={textareaRef}
                      disabled={!isEditMode || disabledField.includes(field.id)}
                      value={field.suggested_value}
                      onChange={(e) => handlePickerChange(e.target.value, index)}
                      className={`text-area-auto text-[12px] border text-pretty leading-[16.9px] font-[400] rounded-[10px] w-full resize-none outline-1 outline-white overflow-auto scrollbar-textArea transition-all ${
                        !isEditMode || disabledField.includes(field.id) ? "" : "bg-white"
                      }`}
                      autoSize={{ minRows: 1, maxRows: 5 }}
                      allowClear={isEditMode}
                    />
                  )}
                </>
              )}

              {/* {isNumber && (
                <Input
                  type="number"
                  value={field.suggested_value}
                  disabled={disabledField.includes(field.id)}
                  onChange={(e) => {
                    const value = e.target.value;
                    // Prevent negative numbers
                    if (!value || parseFloat(value) >= 0) {
                      handlePickerChange(value, index);
                    }
                  }}
                  allowClear
                  className="px-3 py-1 mb-2 border rounded-[10px] w-full no-arrows "
                />
              )} */}

              {isNumber && (
                <>
                  {textSuggestionLoader ? (
                    <div className="w-full mb-2">
                      <Skeleton.Input active block />
                      {/* <Spin indicator={<LoadingOutlined spin />} size="large" /> */}
                    </div>
                  ) : (
                    <Input
                      type="number"
                      value={field.suggested_value}
                      disabled={!isEditMode || disabledField.includes(field.id) || (field.options && field.options.length > 0)}
                      onChange={(e) => {
                        const value = e.target.value;
                        // Prevent negative numbers
                        if (!value || parseFloat(value) >= 0) {
                          handlePickerChange(value, index);
                        }
                      }}
                      allowClear={isEditMode}
                      className="px-3 py-1 text-[12px] border rounded-[10px] w-full no-arrows"
                    />
                  )}
                </>
              )}

              {isCalculation && (
                // <Input
                //   type="number"
                //   value={field.suggested_value}
                //   disabled
                //   className="px-3 py-2 mb-2 border rounded-[10px] w-full no-arrows "
                // />
                <Button
                  shape="round"
                  disabled
                  className="bg-blue-500 text-white border min-w-[80px] h-[25px] text-[12px] p-0"
                  style={{
                    backgroundColor: "rgb(59 130 246)",
                    color: "white",
                  }}
                >
                  {field.suggested_value}
                </Button>
              )}

              {isAPI && (
                <>
                  {apiLodaer ? (
                    <div className="w-full flex justify-center">
                      <Skeleton.Input active block />
                    </div>
                  ) : (
                    // <Input
                    //   type="text"
                    //   value={field.suggested_value}
                    //   disabled
                    //   className="px-3 py-2 mb-2 border rounded-[10px] w-full no-arrows "
                    // />
                    <Button
                      shape="round"
                      disabled
                      className="bg-blue-500 text-white border min-w-[80px] h-[25px] text-[12px] p-0"
                      style={{
                        backgroundColor: "rgb(59 130 246)",
                        color: "white",
                      }}
                    >
                      {field.suggested_value}
                    </Button>
                  )}
                </>
              )}

              {isParentAPI && (
                // <Input
                //   // type="number"
                //   value={field.suggested_value}
                //   disabled
                //   className="px-3 py-2 mb-2 border rounded-[10px] w-full no-arrows "
                // />
                <Button
                  disabled
                  shape="round"
                  className="bg-blue-500 text-white border min-w-[80px] h-[25px] text-[12px] p-0"
                  style={{
                    backgroundColor: "rgb(59 130 246)",
                    color: "white",
                  }}
                >
                  {field.suggested_value}
                </Button>
              )}

              {isDatePicker && (
                <DatePicker
                  value={field.suggested_value && /^\d{2}\/\d{2}\/\d{4}$/.test(field.suggested_value)
                    ? dayjs(field.suggested_value, "DD/MM/YYYY")
                    : null}
                  format="DD/MM/YYYY"
                  // disabledDate={(current) => current && current > dayjs().endOf('day')}
                  disabled={disabledField.includes(field.id)}
                  onChange={(date) => handlePickerChange(date, index)}
                  className="w-full h-[25px]"
                />
              )}

              {isImagePicker && (
                <div className="flex gap-2 items-center">
                  <Button
                    onClick={() => inputRef.current.click()}
                    type="primary"
                    className={`w-[180px] custom-button px-2 rounded-xl !text-[12px] border-opacity-30 bg-[#2F80ED] text-white h-[30px] `}
                    disabled={!isEditMode || disabledField.includes(field.id)}
                  >
                    <Image
                      width={16}
                      height={16}
                      alt="logo"
                      src={"/images/newInspection/image.png"}
                      className="w-[16px] h-[16px]"
                      preview={false}
                    />
                    <p className={`text-white text-[12px] font-[400]`}>
                      {fieldImage !== null || imageUrl !== ""
                        ? t("Replace Image")
                        : t("Select Image")}
                    </p>
                    <input
                      type="file"
                      ref={inputRef}
                      onChange={handleImage}
                      accept="image/*"
                      className="hidden"
                      name=""
                      id=""
                      disabled={!isEditMode || disabledField.includes(field.id)}
                    />
                  </Button>
                  {(fieldImage !== null || imageUrl !== "" || field.suggested_value) && (
                    <Image
                      width={30}
                      height={30}
                      alt=""
                      src={imageUrl || field.suggested_value}
                      className="rounded-md object-cover"
                    />
                  )}
                </div>
              )}

            </div>
          </div>
        );
      })}
    </>
  );
};

export default Suggestions;
