@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 255, 255, 255;
    --background-end-rgb: 255, 255, 255;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

.fixed-bottom-button {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.custom-hover-button {
  background-color: white;
  color: black;
}

.custom-hover-button:hover {
  /* background-color: rgba(47, 128, 237, 0.75) !important; #2F80ED with 30% opacity */
  color: black !important;
}

.ant-select-selector {
  border-radius: 10px !important;
}

/* .custom-modal .ant-modal-body {
    max-height: 70vh; 
    overflow-y: auto; 
    scrollbar-width: thin;
    scrollbar-color: #E5E5E5 transparent;
} */

/* .custom-modal .ant-modal-body::-webkit-scrollbar {
    width: 6px;
}

.custom-modal .ant-modal-body::-webkit-scrollbar-track {
    background: transparent;
}

.custom-modal .ant-modal-body::-webkit-scrollbar-thumb {
    background-color: #E5E5E5; 
    border-radius: 3px; 
}

.custom-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
    background-color: #CCCCCC; 
} */

/* .custom-modal .ant-modal-content {
    max-height: 80vh;
    overflow-y: auto;
} */

.custom-modal .ant-modal-body {
  padding-right: 8px;
}

.custom-modal .ant-modal-body::-webkit-scrollbar {
  width: 6px;
  height: 100%;
}

.custom-modal .ant-modal-body::-webkit-scrollbar-thumb {
  background-color: #e5e5e5;
  border-radius: 3px;
}

.custom-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
  background-color: #cccccc;
}
.custom-modal-sidebar .ant-modal-body {
  padding-right: 8px;
}

.custom-modal-sidebar .ant-modal-body::-webkit-scrollbar {
  width: 6px;
  height: 100%;
}

.custom-modal-sidebar .ant-modal-body::-webkit-scrollbar-thumb {
  background-color: #e5e5e5;
  border-radius: 3px;
}

.custom-modal-sidebar .ant-modal-body::-webkit-scrollbar-thumb:hover {
  background-color: #cccccc;
}

/* .custom-button:hover{
    background-color: #2f81edc1 !important;
    border-color: #B4B4B4 !important;
    opacity: 1 !important;
} */

.custom-button:disabled {
  background-color: #2f80ed !important;
  color: white !important;
  opacity: 1 !important;
}

.custom-button-disable:disabled {
  background-color: #2f81ed6f !important;
  color: white !important;
  opacity: 1 !important;
}


.ant-select-dropdown {
  border-radius: 0 0 10px 10px !important;
}

/* Antd datePicker */
/* .ant-picker-panel-container {
  border-radius: 0 0 10px 10px !important;
}
.ant-picker-dropdown {
  width: 30% !important;
}
.ant-picker-panel-container {
  justify-content: end;
  width: 100% !important;
}
.ant-picker-date-panel {
  width: 100% !important;
}
.ant-picker-time-panel {
  width: 35% !important;
}
.ant-picker-header {
  width: 99% !important;
}
.ant-picker-content {
  width: 99% !important;
}
.ant-picker-time-panel-colum {
  width: 33% !important;
} */

/* class="ant-picker-header-next-btn" */

.ant-picker-header-next-btn{
  background-color: #2F80ED14 !important;
  height: 30px !important;
  width: 40px !important;
  margin-top: 5px;
  padding-right: 2px !important;
  border-radius: 9px !important;
  color: black !important;
}

.ant-picker-header-prev-btn{
  background-color: #2F80ED14 !important;
  height: 30px !important;
  width: 40px !important;
  margin-top: 5px;
  padding-left: 2px !important;
  border-radius: 9px !important;
  color: black !important;
}

.ant-picker-super-next-icon, .ant-picker-super-prev-icon, .ant-picker-header-super-next-btn, .ant-picker-header-super-prev-btn{
  display: none !important;
}


/* .ant-select-dropdown{
  width: inherit !important;
} */

.ant-cascader-menu{
  height: auto !important;
}

/* Set dropdown to match card width and auto-adjust height */
.dropdown-container {
  width: 100%; /* Match dropdown width to the parent card */
}

.dropdown-container {
  width: 100% !important;
}

/* Antd number input field */
/* .ant-input-outlined:hover {
  border-color: #d9d9d9;
  background-color: #ffffff;
}

.ant-input-outlined:focus {
  border-color: #d9d9d9;
  background-color: #ffffff;
} */

.no-arrows::-webkit-outer-spin-button,
.no-arrows::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
.no-arrows[type='number'] {
  -moz-appearance: textfield;
}

/* antd textarea */
.text-area-auto{
  transition: all .3s ease-in-out;
}

.text-area-auto:hover{
  border-color: white !important;
  outline: 1px solid #4096ff !important;
}

.text-area-auto:focus{
  outline: 1px solid #4096ff !important;
}

/* Scollbar */
.scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 100%;
}

.scrollbar::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  border-radius: 3px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #ababab;
}

/* Scollbar-mini */
.scrollbar-mini::-webkit-scrollbar {
  width: 3px;
  height: 100%;
}

.scrollbar-mini::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  border-radius: 3px;
}

.scrollbar-mini::-webkit-scrollbar-thumb:hover {
  background-color: #ababab;
}

/* Antd Cascader menu Scollbar */
.ant-cascader-dropdown div div::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}

.ant-cascader-dropdown div div::-webkit-scrollbar-thumb {
  background-color: #d1d1d1 !important;
  border-radius: 3px !important;
}

.ant-cascader-dropdown div div::-webkit-scrollbar-thumb:hover {
  background-color: #ababab !important;
}

/* Scollbar */
.scrollbar-textArea::-webkit-scrollbar {
  width: 4px;
  height: 100%;
}

.scrollbar-textArea::-webkit-scrollbar-thumb {
  background-color: #e9e9e9;
  border-radius: 3px;
}

.scrollbar-textArea::-webkit-scrollbar-thumb:hover {
  background-color: #ababab;
  cursor: default;
}

.measure-Scroll::-webkit-scrollbar {
  width: 2px;
  height: 100%;
}

/* Multiple selection tag styles */
.teamMember-selector .ant-select-selection-overflow-item{
  align-items: center !important;
  
}

.teamMember-selector .ant-select-selection-item,
.teamMember-selector .ant-select-selection-item
{
  font-size: 15px !important;
  border-radius: 2px !important;
  height: 46px !important;
  align-items: center !important;
  border-radius: 6px !important;
}

.teamMember-selector .ant-select-selector{
  min-height: 55px !important;
}

.no-hover:hover td{
  background-color: #e5e5e5 !important; /* Prevents hover background color change */
}

/* Custom media modal */

.custom-modal .ant-modal-content {
  padding: 0 !important; /* Override default padding */
  border-radius: 10px !important;
  /* width: 40vw !important; */
  height: 60vh !important;
}

.custom-modal .ant-modal-content .ant-modal-body {
  padding: 0 !important; /* Override default padding */
  border-radius: 10px !important;
  /* width: 40vw !important; */
  height: 60vh !important;
}

/* .ant-select-selection-placeholder {
  color: black !important;
} */

.ant-select-selection-placeholder:focus {
  color: rgb(106, 106, 106) !important;
}

/* .text-area-clear{
  padding: 8px !important;
} */

/* .text-area-clear .ant-input-clear-icon{
  padding: 4px !important;
  font-size: 20px !important;
} */

.ant-input-clear-icon {
  font-size: 12px !important;
  padding: 0 !important;
  margin: 0 !important;
  position: relative !important;
  top: 0.5px !important;
  color: #000;
}

.custom-cancel-button:hover {
  color: #F0142F !important;
  border-color: #F0142F !important;
}

.ant-select-selection-item{
  font-size: 12px !important;
}

/* ======================================================== */
.file-drag-active {
  cursor: copy !important;
}

.scroll-left {
  left: 20px;
}

.scroll-right {
  right: 20px;
}

.file-drag-active .scroll-indicator:hover {
  opacity: 0.8;
}

/* Improve drop zones during file drag */
.file-drag-active .drop-zone {
  border: 2px dashed #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

/* ======================================================== */