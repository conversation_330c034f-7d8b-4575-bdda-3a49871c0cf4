// Modern Firebase Authentication with Account Linking
"use client";
import {
  getAuth,
  signInWithPopup,
  GoogleAuthProvider,
  OAuthProvider,
  EmailAuthProvider,
  signInWithEmailAndPassword,
  linkWithCredential,
  linkWithPopup,
  getAdditionalUserInfo,
} from "firebase/auth";

/**
 * Universal sign-in function that handles all providers
 * and manages authentication errors, including account linking
 */
export const signInWithProvider = async (providerType: any) => {
  const auth = getAuth();
  let provider;

  // Set up the requested provider
  switch (providerType) {
    case "google":
      provider = new GoogleAuthProvider();
      break;
    case "microsoft":
      provider = new OAuthProvider("microsoft.com");
      break;
    case "apple":
      provider = new OAuthProvider("apple.com");
      break;
    default:
      throw new Error(`Unsupported provider: ${providerType}`);
  }

  try {
    // First attempt with the requested provider
    const result: any = await signInWithPopup(auth, provider);
    const isNewUser = getAdditionalUserInfo(result)?.isNewUser;

    // console.log("result", result);
    // console.log("isNewUser", isNewUser);

    console.log(`Successfully signed in with ${providerType}`);
    return {
      user: result.user,
      isNewUser,
    };
  } catch (error: any) {
    console.log("Sign-in error:", error.code, error.message);

    // Handle the account exists with different credential error
    if (error.code === "auth/account-exists-with-different-credential") {
      const email = error.customData.email;
      const pendingCredential = error.customData.credential;

      // Show the user appropriate messaging
      const confirmResult = await showConfirmDialog(
        `The email ${email} is already associated with a different login method. Would you like to link this account with your current sign-in method?`
      );

      if (!confirmResult) {
        throw new Error("User cancelled account linking");
      }

      try {
        // We need to have the user sign in with the existing provider first
        // Let them know they need to sign in with their original method first
        const methodMessage = `Please sign in with your original authentication method for ${email} first.`;
        await showMessage(methodMessage);

        // Here we redirect users to sign in with their original method
        // (you'll need to implement a UI for this)
        const originalMethod = await promptUserForOriginalMethod();

        let result: any;
        if (originalMethod === "email") {
          // If their original method was email password, prompt for password
          const password: any = await promptForPassword(email);
          if (password?.trim() !== "") {
            result = await signInWithEmailAndPassword(auth, email, password);
          } else {
          }
        } else {
          // Otherwise use the appropriate provider
          const originalProvider = getProviderForMethod(originalMethod);
          result = await signInWithPopup(auth, originalProvider);
        }

        // Now link the new credential to this account
        if (pendingCredential) {
          await linkWithCredential(result.user, pendingCredential);
          message.success("Account successfully linked!")
        } else {
          // If we don't have the credential, we need to redo the popup
          await linkWithPopup(result.user, provider);
          message.success("Account successfully linked!")
        }

        return {
          user: result.user,
          isNewUser: false,
          wasLinked: true,
        };
      } catch (linkError: any) {
        console.error("Error during account linking:", linkError);
        throw new Error(`Failed to link accounts. ${linkError.message}`);
      }
    } else {
      // For any other error, just rethrow
      throw error;
    }
  }
};

/**
 * Helper function to get provider for a method
 */
function getProviderForMethod(method: any) {
  switch (method) {
    case "google.com":
      return new GoogleAuthProvider();
    case "microsoft.com":
      return new OAuthProvider("microsoft.com");
    case "apple.com":
      return new OAuthProvider("apple.com");
    default:
      throw new Error(`Unsupported provider: ${method}`);
  }
}

/**
 * These functions should be implemented according to your UI framework
 * (React, Vue, etc.)
 */
import { Modal, message, Input } from "antd";

async function showConfirmDialog(msg: string): Promise<boolean> {
  return new Promise((resolve) => {
    Modal.confirm({
      title: "Confirmation",
      content: msg,
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });
}

async function showMessage(msg: string): Promise<void> {
  message.info(msg);
}

async function promptUserForOriginalMethod(): Promise<string> {
  return new Promise((resolve, reject) => {
    Modal.info({
      title: "Select Original Sign-in Method",
      content: (
        <div>
          <p>Please select your original sign-in method:</p>
          <ul style={{ listStyleType: "none", padding: 0 }}>
            <li style={{ cursor: "pointer" }}>
              <button
                onClick={() => {
                  Modal.destroyAll();
                  resolve("google.com");
                }}
              >
                Google
              </button>
            </li>
            <li style={{ cursor: "pointer" }}>
              <button
                onClick={() => {
                  Modal.destroyAll();
                  resolve("microsoft.com");
                }}
              >
                Microsoft
              </button>
            </li>
            <li style={{ cursor: "pointer" }}>
              <button
                onClick={() => {
                  Modal.destroyAll();
                  resolve("apple.com");
                }}
              >
                Apple
              </button>
            </li>
            <li style={{ cursor: "pointer" }}>
              <button
                onClick={() => {
                  Modal.destroyAll();
                  resolve("email");
                }}
              >
                Email/Password
              </button>
            </li>
          </ul>
        </div>
      ),
      okButtonProps: { style: { display: "none" } },
      onCancel: () => reject(new Error("No method selected")),
    });
  });
}

async function promptForPassword(email: string): Promise<string> {
  return new Promise((resolve) => {
    let password = "";

    Modal.confirm({
      title: "Password Required",
      content: (
        <div>
          <p>Please enter your password for {email}:</p>
          <Input.Password
            onChange={(e) => {
              password = e.target.value;
            }}
            onPressEnter={() => {
              Modal.destroyAll();
              resolve(password);
            }}
          />
        </div>
      ),
      onOk: () => resolve(password),
      onCancel: () => resolve(""),
    });
  });
}

// ----- USAGE EXAMPLE -----
// In your login component:
// const handleGoogleLogin = async () => {
//   try {
//     const { user, isNewUser, wasLinked } = await signInWithProvider('google');
//     if (isNewUser) {
//       // Create user in your database
//     } else if (wasLinked) {
//       // Update user info if needed after linking
//     }
//     // Continue with post-login flow
//   } catch (error) {
//     // Show error to user
//   }
// };

// import 'antd/dist/antd.css'; // or the appropriate path for your Ant Design version
