"use client";
import React, { useEffect, useState, useRef, useCallback } from "react";
import Navbar from "@/components/Navbar/navbar";
import Sidebar from "@/components/sidebar/sidebar";
import { <PERSON><PERSON>, Poppins, Open_Sans } from "next/font/google";
import Image from "next/image";
import { Button, Input, Modal, Spin } from "antd";
import { LoadingOutlined, RightOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { useLoaderContext } from "@/context/LoaderContext";
import Loader from "@/components/loader/loader";
import { message } from "antd";
import {
  fetchAllDocuments,
  deleteInspection,
  getInspections,
} from "@/src/services/home.api";
import { inspectionTypeList } from "@/src/libs/constants";
import DeleteModal from "@/components/deleteModal/deleteModal";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import InspectionTable from "./inspectionsTable";
import InspectionMap from "./inspectionsmapView";
import ExportModal from "./exportModal";
import ActionButtons from "./actionButtons";
import { useSidebarContext } from "@/context/sidebarContext";
import debounce from "debounce";
import InfiniteScroll from "react-infinite-scroll-component";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Inspections = () => {
  const { isCollapsed } = useSidebarContext();

  const [Inspections, setInspections] = useState<any>([]);
  const [searchedInspections, setSearchedInspections] = useState<any>([]);
  const [searchInspection, setSearchInspection] = useState<any>("");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [deleteId, setDeleteId] = useState("");
  const [refresh, setRefresh] = useState(false);
  const [inspectionTypeList, setInspectionTypeList] = useState<any>(null);
  const [isMapView, setIsMapView] = useState(false);
  const [selectedInspections, setSelectedInspections] = useState<any>([]);
  const [projectName, setProjectName] = useState("");
  const [isActionMenuVisible, setIsActionMenuVisible] = useState(false);

  const [search, setSearch] = useState<any>("");
  const [hasMany, setHasMany] = useState(false);
  const [lastDoc, setLastDoc] = useState<string | null>(null);
  const [searchLoader, setSearchLoader] = useState(false);

  const router = useRouter();
  const { t } = useTranslation();

  const { loader, setLoader } = useLoaderContext();

  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsActionMenuVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const fetchInspections = async (projectId: any) => {
    setLoader(true);
    const inspectionsArray: any = await getInspections({
      search,
      projectId,
      isForFirst: true,
      lastDocument: lastDoc,
      limitValue: 20,
    });
    if (inspectionsArray) {
      setInspections(inspectionsArray.list);
      setSearchedInspections(inspectionsArray.list);
      setHasMany(inspectionsArray.hasMore);
      setLastDoc(inspectionsArray.lastDocument);

      const reportTypesString: any = localStorage.getItem("inspectionTypes");
      const reportTypes = JSON.parse(reportTypesString);
      setInspectionTypeList(reportTypes);
      setLoader(false);
    } else {
      setLoader(false);
      message.error(t("Something went wrong!"));
    }
  };

  const fetchMoreInspections = async () => {
    const projectId: any = localStorage.getItem("ScalarProjectId");
    const inspectionsArray: any = await getInspections({
      search: search.toLocaleLowerCase(),
      projectId,
      isForFirst: false,
      lastDocument: lastDoc,
      limitValue: 20,
    });
    if (inspectionsArray) {
      setInspections((prev: any) => [...prev, ...inspectionsArray.list]);
      setSearchedInspections((prev: any) => [
        ...prev,
        ...inspectionsArray.list,
      ]);
      setHasMany(inspectionsArray.hasMore);
      setLastDoc(inspectionsArray.lastDocument);
    } else {
      message.error(t("Something went wrong!"));
    }
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    // if (value.trim() === "") return;
    debouncedSearchInspections(value.trim());
  };

  const debouncedSearchInspections = useCallback(
    debounce(async (value: string) => {
      try {
        setSearchLoader(true);
        const projectId: any = localStorage.getItem("ScalarProjectId");

        const inspectionsArray: any = await getInspections({
          search: value.toLocaleLowerCase(),
          projectId,
          isForFirst: true,
          lastDocument: lastDoc,
          limitValue: 20,
        });

        if (inspectionsArray) {
          setInspections(inspectionsArray.list);
          setSearchedInspections(inspectionsArray.list);
          setHasMany(inspectionsArray.hasMore);
          setLastDoc(inspectionsArray.lastDocument);

          const reportTypesString: any =
            localStorage.getItem("inspectionTypes");
          const reportTypes = JSON.parse(reportTypesString);
          setInspectionTypeList(reportTypes);
          setLoader(false);
        } else {
          setLoader(false);
          message.error(t("Something went wrong!"));
        }
      } catch (error) {
        message.error(t("Something went wrong!"));
      } finally {
        setSearchLoader(false);
      }
    }, 500), // 1000ms delay
    []
  );

  useEffect(() => {
    const laguage: any = localStorage.getItem("ScalarLanguage");
    handleChangeLanguage(laguage);
    localStorage.removeItem("ViewInspectionDetails");
    const userId = localStorage.getItem("ScalarUserId");
    const projectId: any = localStorage.getItem("ScalarProjectId");
    const pName: any = localStorage.getItem("ScalarProjectName");
    setProjectName(pName);

    fetchInspections(projectId);
  }, [refresh]);

  useEffect(() => {
    const searchArray = Inspections.filter((item: any) => {
      return item.name
        .toLowerCase()
        .includes(searchInspection.trim().toLowerCase());
    });
    setSearchedInspections(searchArray);
  }, [searchInspection]);

  const handleDelete = async (e: any) => {
    e.preventDefault();

    const inspectionDetails: any = {
      is_deleted: true,
    };
    setLoader(true);
    const projectId: any = localStorage.getItem("ScalarProjectId");
    const res: any = await deleteInspection(
      inspectionDetails,
      deleteId,
      projectId
    );
    if (res) {
      message.success(t("Successfully deleted!"));
      setLoader(false);
      setIsModalOpen(false);
      setRefresh(!refresh);
    } else {
      setLoader(false);
      setIsModalOpen(false);
      setRefresh(!refresh);
      message.error(t("Something went wrong, try again later!"));
    }
  };

  const handleInspectionClick = (id: any, name: any, orderArray: any) => {
    const inspectionName =
      name.slice(0, 1).toUpperCase() + name.slice(1).toLowerCase();
    localStorage.setItem("ScalarInspectionId", id);
    localStorage.setItem("ScalarInspectionName", inspectionName);
    if (orderArray) {
      const orderIds = orderArray.map((item: any) => item.id);
      localStorage.setItem("findingOrder", JSON.stringify(orderIds));
    }
    router.push("/newInspection");
  };

  return (
    <>
      {loader && <Loader />}
      <div className="flex h-screen">
        <Sidebar />
        <div
          className={`${
            isCollapsed ? "w-[calc(100vw-60px)]" : "w-[calc(100vw-16%)]"
          }`}
        >
          <Navbar
            search={false}
            searchInspection={searchInspection}
            setSearch={setSearchInspection}
          />
          <div
            className={`h-[calc(100%-60px)] text-black ${poppins.className} pb-10 relative`}
          >
            <div className="w-full bg-white flex justify-between items-center px-10 pt-6 pb-1 sticky top-0 z-10">
              <h1 className="text-[24px] leading-[24px] font-[500]">
                {/* {t("Projects")} */}
                <span
                  className="cursor-pointer hover:underline"
                  onClick={() => router.push("/home")}
                >
                  {t("Projects")}
                </span>
                {" "}
                  <RightOutlined className="text-[18px]" />
                  {" "}
                {projectName}
              </h1>
              <Button
                onClick={() => router.push(`/newInspection/inspectionMetaData?prev=Project&pname=${projectName}`)}
                type="primary"
                className={`${poppins.className} custom-button px-4 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px] `}
              >
                <Image
                  width={20}
                  height={20}
                  alt="logo"
                  src={"/images/home/<USER>"}
                  className="w-[20px] h-[20px]"
                />
                <p className={`text-white text-[14px] font-[400]`}>
                  {t("New Inspection")}
                </p>
              </Button>
            </div>
            <div className="px-10 bg-white flex justify-between items-center pt-2 pb-6 z-10">
              <Input
                className="w-[40%] px-3 h-[35px] bg-[#2F80ED0D] border-none"
                placeholder={t("Search")}
                value={search}
                suffix={
                  <Image
                    width={12}
                    height={12}
                    className={`${
                      search !== ""
                        ? "w-[30px] relative left-2 cursor-pointer"
                        : "w-[12px]"
                    } ${search !== "" ? "h-[30px]" : "h-[12px]"}`}
                    src={
                      search.length !== 0
                        ? "/images/cancel.svg"
                        : "/images/Icon.png"
                    }
                    alt="icon"
                    onClick={
                      search !== ""
                        ? () => {
                            setSearch("");
                            debouncedSearchInspections("");
                          }
                        : () => {}
                    }
                  />
                }
                onChange={(e) => handleSearch(e.target.value)}
              />
              <div className="flex gap-2">
                {isMapView && (
                  <Button
                    onClick={() => setIsMapView(false)}
                    type="primary"
                    className={`${poppins.className} w-[180px] custom-button-disable px-4 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px] `}
                    disabled={searchedInspections.length === 0}
                  >
                    <Image
                      width={20}
                      height={20}
                      alt="logo"
                      src={"/images/newInspection/list.png"}
                      className="w-[20px] h-[20px]"
                    />
                    {t("List view")}
                  </Button>
                )}
                {!isMapView && (
                  <Button
                    onClick={() => setIsMapView(true)}
                    type="primary"
                    className={`${poppins.className} w-[180px] custom-button-disable px-4 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px]`}
                    disabled={searchedInspections.length === 0}
                  >
                    <Image
                      width={20}
                      height={20}
                      alt="logo"
                      src={"/images/newInspection/map.png"}
                      className="w-[20px] h-[20px]"
                    />
                    {t("Map view")}
                  </Button>
                )}
                <div className="relative">
                  <Button
                    onClick={() => setIsActionMenuVisible(!isActionMenuVisible)}
                    type="primary"
                    className={`${poppins.className} w-[100px] custom-button-disable px-4 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px]`}
                    disabled={searchedInspections.length === 0}
                  >
                    {t("Actions")}
                  </Button>
                  {isActionMenuVisible && (
                    <ActionButtons
                      setSearchedInspections={setSearchedInspections}
                      searchedInspections={searchedInspections}
                      setIsActionMenuVisible={setIsActionMenuVisible}
                      selectedInspections={selectedInspections}
                      setRefresh={setRefresh}
                      t={t}
                    />
                  )}
                </div>
              </div>
            </div>
            {searchedInspections.length === 0 ? (
              <div className="w-full h-[calc(100vh-208px)] mt-26 flex justify-center items-center">
                <div className="w-[352px] text-left">
                  <div className="w-full flex justify-center items-center">
                    <Image
                      src={"/images/home/<USER>"}
                      width={352}
                      height={337}
                      alt={t(
                        "Start a new inspection by clicking New Inspection"
                      )}
                      className="w-[35vh]"
                    />
                  </div>
                  <p className="text-[14px] leading-[20.8px] text-center font-[400] text-[#626d7d] mt-4">
                    {t("Start a new inspection by clicking New Inspection")}
                  </p>
                </div>
              </div>
            ) : (
              <div
                id="inspectionScroll"
                className="px-10 h-[calc(100vh-208px)] overflow-auto scrollbar"
              >
                {!isMapView ? (
                  <>
                    {!searchLoader && (
                      <InfiniteScroll
                        dataLength={searchedInspections.length}
                        next={fetchMoreInspections}
                        hasMore={hasMany}
                        loader={
                          <div className="w-full flex justify-center items-center gap-2 mb-6">
                            <Spin
                              size="default"
                              indicator={<LoadingOutlined spin />}
                            />
                            {t("Loading")}...
                          </div>
                        }
                        scrollThreshold={0.8}
                        scrollableTarget="inspectionScroll"
                      >
                        <InspectionTable
                          {...{
                            searchedInspections,
                            inspectionTypeList,
                            setDeleteId,
                            setIsModalOpen,
                            handleInspectionClick,
                            selectedInspections,
                            setSelectedInspections,
                          }}
                        />
                      </InfiniteScroll>
                    )}
                    {searchLoader && (
                      <div className="w-full h-full flex justify-center items-center gap-2 mb-4">
                        <Spin
                          size="large"
                          indicator={<LoadingOutlined spin />}
                        />
                        {t("Searching")}...
                      </div>
                    )}
                  </>
                ) : (
                  <div className="w-full h-[calc(100vh-242px)] flex justify-center items-center bg-gray-200 rounded-[12px]">
                    {!searchLoader && (
                      <InspectionMap
                        inspections={searchedInspections}
                        googleMapsApiKey={process.env.NEXT_GOOGLE_PLACE_API}
                        handleInspectionClick={handleInspectionClick}
                      />
                    )}
                    {searchLoader && (
                      <div className="w-full h-full flex justify-center items-center gap-2 mb-4">
                        <Spin
                          size="large"
                          indicator={<LoadingOutlined spin />}
                        />
                        Serching...
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        {isModalOpen && (
          <DeleteModal
            handleDelete={handleDelete}
            setDeleteId={setDeleteId}
            setIsModalOpen={setIsModalOpen}
          />
        )}
      </div>
    </>
  );
};

export default Inspections;
