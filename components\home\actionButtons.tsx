import React, { useState } from "react";
import { Button, message } from "antd";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { Poppins } from "next/font/google";
import ExportModal from "./exportModal";
import DeleteModal from "./deleteModal";

const poppins = Poppins({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const ActionButtons = ({
  setRefresh,
  setIsActionMenuVisible,
  selectedProjects,
  t,
}: any) => {
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const exportProjects = async () => {
    if (selectedProjects.length === 0) {
      message.info(t("Select projects to export."));
      return;
    }
    setIsExportModalOpen(true);
  };

  const deleteProjects = async () => {
    if (selectedProjects.length === 0) {
      message.info(t("Select projects to delete."));
      return;
    }
    setIsDeleteModalOpen(true);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="p-2 absolute top-[110%] right-0 z-50 bg-white rounded-2xl flex flex-col gap-2 shadow-md border"
    >
      <Button
        onClick={exportProjects}
        type="primary"
        className={`${poppins.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white`}
      >
        {t("Export")}
        </Button>
        <ExportModal
          isModalOpen={isExportModalOpen}
          setIsModalOpen={setIsExportModalOpen}
          selectedProjects={selectedProjects}
          setMenu={setIsActionMenuVisible}
        />
      <Button
        onClick={deleteProjects}
        style={{
          backgroundColor: "#FF5656",
          color: "white",
          borderColor: "transparent",
        }}
        className={`${poppins.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] border-opacity-30 bg-[#FF5656] text-white `}
      >
        {t("Delete")}
      </Button>
      {/* {isDeleteModalOpen && ( */}
        <DeleteModal
          setIsModalOpen={setIsDeleteModalOpen}
          selectedProjects={selectedProjects}
          setRefresh={setRefresh}
          isModalOpen={isDeleteModalOpen}
          setMenu={setIsActionMenuVisible}
        />
      {/* )} */}
      <Button
        onClick={() => {
          setIsActionMenuVisible(false);
        }}
        type="primary"
        style={{
          backgroundColor: "#ff91000a",
          color: "#FF9200",
        }}
        className={`${poppins.className} w-full custom-button px-4 py-5 rounded-xl text-[15px] text-[#FF9200] border border-[#FF9200] bg-[#ff91000a] hover:bg-[#ff910015]`}
      >
        {t("Cancel")}
      </Button>
    </motion.div>
  );
};

export default ActionButtons;
