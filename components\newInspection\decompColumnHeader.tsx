import { Spin } from "antd";
import React from "react";
import { LoadingOutlined } from "@ant-design/icons";

const DecompColumnHeader = ({
    group,
    provided,
    groupedFindings,
    setWholeGroup,
    setIsEditDecompModalOpen,
    setIsApproveDecompModalOpen,
    loadingIds,
}: any) => {
  return (
    <div
      {...provided.dragHandleProps}
      className={`p-3 ${
        group.isApproved ? "bg-[#1677ff]" : "bg-[#1677ffc6] animate-pulse"
      } rounded-t-lg font-medium flex gap-2 justify-between items-center text-white sticky top-0 z-10 break-words text-[12px] hover:!cursor-pointer`}
      onClick={(e) => {
        e.stopPropagation();
        const currentGroup = groupedFindings.find(
          (g: any) => g.id === group.id
        );
        if (currentGroup) {
          setWholeGroup(currentGroup);
        } else {
          setWholeGroup({
            id: group.id,
            parent: group.decomposition,
            title_fields: group.decomposition.title_fields,
            decompositionItem: [],
          });
        }
        sessionStorage.setItem("currentId", group.id);
        const id: any = process.env.NEXT_DECOMP_ID;
        sessionStorage.setItem("FindingTypeId", id);
        localStorage.setItem("isFindingUpdate", "true");
        localStorage.setItem("isMeasureUpdate", "false");
        localStorage.setItem("findingId", group.id);
        localStorage.setItem(
          "updateFindingObj",
          JSON.stringify(group.decomposition.field_values)
        );
        if (group.isApproved) {
          setIsEditDecompModalOpen(true);
        } else {
          setIsApproveDecompModalOpen(true);
        }
      }}
    >
      <span>{group.title || "Decomposition"}</span>
      {!group.isApproved && loadingIds.includes(group.id) && (
        <Spin
          indicator={<LoadingOutlined spin className="text-white" />}
          size="small"
        />
      )}
    </div>
  );
};

export default DecompColumnHeader;
