"use client";
import React, { createContext, useContext, useState } from "react";

const LoaderContext = createContext();

export const LoaderContextProvider = ({ children }) => {
  const [loader, setLoader] = useState(false);

  return (
    <LoaderContext.Provider
      value={{
        loader,
        setLoader,
      }}
    >
      {children}
    </LoaderContext.Provider>
  );
};

export const useLoaderContext = () => {
  return useContext(LoaderContext);
};
