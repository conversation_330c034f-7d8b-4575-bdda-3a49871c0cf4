"use client";
import React, { useEffect, useState } from "react";
import Navbar from "@/components/Navbar/navbar";
import Sidebar from "@/components/sidebar/sidebar";
import { Poppins } from "next/font/google";
import { Button } from "antd";
import { useRouter } from "next/navigation";
import { useLoaderContext } from "@/context/LoaderContext";
import Loader from "@/components/loader/loader";
import { message } from "antd";
import { fetchAllDocuments } from "@/src/services/newInspection.api";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import { useSidebarContext } from "@/context/sidebarContext";
import { useFindingContext } from "@/context/findingContext";
import { DecompositionGroup, OrderItem } from "../newInspection/types";
import {
  generateDecompTitle,
  generateFindingTitle,
} from "../newInspection/utils";
import FindingOverviewTable from "./findingOverviewTable";
import { fetchInspection } from "@/src/services/inspectionDetails.api";
import { mapFiledsWithValue } from "@/src/services/fieldsMapping";
import { RightOutlined } from "@ant-design/icons";

const poppins = Poppins({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const FindingOverview = () => {
  const { isCollapsed } = useSidebarContext();
  const [searchInspection, setSearchInspection] = useState<any>(null);
  const [refresh, setRefresh] = useState(false);
  const router = useRouter();

  const { loader, setLoader } = useLoaderContext();
  const { t } = useTranslation();

  const [groupedFindings, setGroupedFindings] = useState<any[]>([]);
  const [inspectionName, setInspectionName] = useState("");
  const [orderArray, setOrderArray] = useState<any[]>([]);
  const [decompositionGroups, setDecompositionGroups] = useState<any[]>([]);

  useEffect(() => {
    const language: any = localStorage.getItem("ScalarLanguage");
    handleChangeLanguage(language);
    localStorage.removeItem("ViewInspectionDetails");
    fetchFindings();
  }, [refresh]);

  const fetchFindings = async () => {
    setLoader(true);
    const inspectionId = localStorage.getItem("ScalarInspectionId");
    const fetchedFindings: any = await fetchAllDocuments(inspectionId); // Fetch findings from the API
        const inspectionDetails: any = await fetchInspection(inspectionId);
    
        const decompId = process.env.NEXT_DECOMP_ID;
        const findingId = process.env.NEXT_FINDING_ID;
    
        const allFindings = fetchedFindings.map((finding: any) => {
          const findingTypeId = finding?.finding_type?.id;
          const suggestionField =
            findingTypeId === decompId
              ? JSON.parse(inspectionDetails?.decomp_fields)?.fields
              : findingTypeId === findingId
              ? JSON.parse(inspectionDetails?.finding_fields)?.fields
              : JSON.parse(inspectionDetails?.measure_fields)?.fields;
    
          const mappedFields = mapFiledsWithValue(suggestionField, finding?.fields);
          return {
            ...finding,
            fields: JSON.stringify(mappedFields),
          };
        });

    if (allFindings) {
      // Function to build hierarchical structure for findings
      const buildHierarchy = (parentId: string | null): any[] => {
        // Find all findings that have the given parentId
        const findings = allFindings.filter(
          (finding: any) => finding.parent_finding_id === parentId
        );

        // Recursively build children for each finding
        return findings.map((finding: any) => ({
          ...finding,
          children: buildHierarchy(finding.id),
        }));
      };

      // Create the grouped structure with all parents and their descendants
      const grouped = buildHierarchy(null).map((parent) => ({
        id: parent.id,
        parent: parent,
        title_fields: parent.title_fields,
        decompositionItem: flattenFindings(parent),
      }));

      const groupedWithoutChildrenArray = grouped.map((group: any) => {
        const items = group.decompositionItem.map((i: any) => {
          const { children, ...rest } = i;
          return rest;
        });

        const { children, ...rest } = group.parent;

        return {
          ...group,
          decompositionItem: items,
          parent: rest,
        };
      });

      setGroupedFindings(groupedWithoutChildrenArray);
      setLoader(false);
    } else {
      setLoader(false);
      message.error(t("Something went wrong!"));
    }
  };

  const flattenFindings = (finding: any): any[] => {
    const children = finding.children ?? [];
    return [finding, ...children.flatMap(flattenFindings)];
  };

  // Use effect for order changes
  useEffect(() => {
    if (groupedFindings.length > 0) {
      // Transform groupedFindings into decompositionGroups
      const groups = groupedFindings.map((group: any) => ({
        id: group.id,
        title: generateDecompTitle(
          group.title_fields,
          group.decompositionItem[0].fields
        ),
        decomposition: group.parent,
        findings: group.decompositionItem
          .filter(
            (item: any) =>
              item?.finding_type?.id === process.env.NEXT_FINDING_ID ||
              item?.finding_type?.id === "83mi507Lv3BizK7bJ7Hg"
          )
          .map((item: any) => ({
            ...item,
            title: generateFindingTitle(
              item.title_fields,
              item.fields,
              0,
              group.parent
            ),
          })),
        isApproved: group?.parent?.approved,
      }));

      // Generate initial order array
      const findingOrderString: any = localStorage.getItem("findingOrder");
      if (findingOrderString) {
        const findingOrder = JSON.parse(findingOrderString);

        // Create a comprehensive mapping of all items first
        const newOrder: OrderItem[] = [];
        groupedFindings.forEach((group: any) => {
          // Add decomposition
          newOrder.push({
            id: group.id,
            type: "decomposition",
            parentId: null,
          });

          // Add findings and their measures
          group.decompositionItem.forEach((finding: any) => {
            if (
              finding?.finding_type?.id === process.env.NEXT_FINDING_ID ||
              finding?.finding_type?.id === "83mi507Lv3BizK7bJ7Hg"
            ) {
              newOrder.push({
                id: finding.id,
                type: "finding",
                parentId: group.id,
              });

              // Add measures
              const measures = group.decompositionItem.filter(
                (item: any) => item.parent_finding_id === finding.id
              );
              measures.forEach((measure: any) => {
                newOrder.push({
                  id: measure.id,
                  type: "measure",
                  parentId: finding.id,
                });
              });
            }
          });
        });

        // Create an ordered array based on the saved findingOrder
        const initialOrder: OrderItem[] = [];

        // First add all items that exist in the saved order
        findingOrder.forEach((id: string) => {
          const item = newOrder.find((i) => i.id === id);
          if (item) {
            initialOrder.push(item);
          }
        });

        // Then add any new items that weren't in the saved order
        newOrder.forEach((item) => {
          if (!initialOrder.some((i) => i.id === item.id)) {
            initialOrder.push(item);
          }
        });

        setOrderArray(initialOrder);

        // REORDERING BOTH GROUPS AND FINDINGS WITHIN GROUPS

        // 1. First, reorder the decompositionGroups based on the findingOrder
        const orderedGroups: DecompositionGroup[] = [];
        const processedGroupIds = new Set<string>();

        // Extract unique group IDs from the order, maintaining their order
        initialOrder.forEach((item) => {
          if (
            item.type === "decomposition" &&
            !processedGroupIds.has(item.id)
          ) {
            processedGroupIds.add(item.id);
            const group = groups.find((g: any) => g.id === item.id);
            if (group) {
              // Deep clone the group to modify its findings
              orderedGroups.push({ ...group, findings: [...group.findings] });
            }
          }
        });

        // Add any groups not found in the order
        groups.forEach((group: any) => {
          if (!processedGroupIds.has(group.id)) {
            orderedGroups.push({ ...group, findings: [...group.findings] });
          }
        });

        // 2. For each group, reorder its findings based on findingOrder
        orderedGroups.forEach((group) => {
          // Get all findings for this group from initialOrder to determine their sequence
          const findingsInOrder = initialOrder
            .filter(
              (item) => item.type === "finding" && item.parentId === group.id
            )
            .map((item) => item.id);

          // Create a map for efficient lookup
          const findingMap = new Map(
            group.findings.map((finding: any) => [finding.id, finding])
          );

          // Create a new ordered findings array
          const orderedFindings: any[] = [];

          // First add findings in the order they appear in findingOrder
          findingsInOrder.forEach((id) => {
            const finding = findingMap.get(id);
            if (finding) {
              orderedFindings.push(finding);
              findingMap.delete(id);
            }
          });

          // Add any remaining findings that weren't in the order
          findingMap.forEach((finding) => {
            orderedFindings.push(finding);
          });

          // Update the group's findings with the ordered list
          group.findings = orderedFindings;
        });

        setDecompositionGroups(orderedGroups);
      } else {
        // Default ordering if no saved order exists
        const initialOrder: OrderItem[] = [];
        groupedFindings.forEach((group: any) => {
          // Add decomposition
          initialOrder.push({
            id: group.id,
            type: "decomposition",
            parentId: null,
          });

          // Add findings and their measures
          group.decompositionItem.forEach((finding: any) => {
            if (
              finding.finding_type.id === process.env.NEXT_FINDING_ID ||
              finding.finding_type.id === "83mi507Lv3BizK7bJ7Hg"
            ) {
              initialOrder.push({
                id: finding.id,
                type: "finding",
                parentId: group.id,
              });

              // Add measures
              const measures = group.decompositionItem.filter(
                (item: any) => item.parent_finding_id === finding.id
              );
              measures.forEach((measure: any) => {
                initialOrder.push({
                  id: measure.id,
                  type: "measure",
                  parentId: finding.id,
                });
              });
            }
          });
        });

        setOrderArray(initialOrder);
        setDecompositionGroups(groups);
      }
    }
  }, [groupedFindings]);

  return (
    <>
      {loader && <Loader />}
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div
          className={`${
            isCollapsed ? "w-[calc(100vw-60px)]" : "w-[calc(100vw-16%)]"
          }`}
        >
          <Navbar
            search={false}
            searchInspection={searchInspection}
            setSearch={setSearchInspection}
          />
          <div
            className={`h-[calc(100%-60px)] text-black ${poppins.className} overflow-hidden`}
          >
            <div className="w-full flex justify-between items-center px-10 py-6 mb-3 sticky top-0 z-10 bg-white border-b">
              <h1 className="text-[24px] leading-[24px] font-[500]">
                {/* {inspectionName !== "" ? inspectionName : t("Finding Overview")} */}
                <span
                  className="cursor-pointer hover:underline"
                  onClick={() => router.push("/newInspection")}
                >
                  {localStorage.getItem("ScalarInspectionName") ||
                    t("Inspection")}
                </span>
                {" "}
                  <RightOutlined className="text-[18px]" />
                  {" "}
                {t("Finding Overview")}
              </h1>
              <div className="flex gap-3">
                <Button
                  onClick={() => router.push("/newInspection")}
                  className="text-[14px] h-[44px] px-6 text-[#2F80ED] border-[#2F80ED] border leading-[14px] font-[500] bg-[#2F80ED] bg-opacity-5 flex items-center gap-2 p-3 rounded-[10px]"
                >
                  {t("Back")}
                </Button>
                <div className="relative">
                  <Button
                    onClick={() =>
                      router.push("/newInspection/measureOverview")
                    }
                    type="primary"
                    className={`${poppins.className} custom-button px-8 rounded-xl text-[15px] border-opacity-30 bg-[#2F80ED] text-white h-[44px]`}
                  >
                    <p className={`text-white text-[14px] font-[400]`}>
                      {t("Next")}
                    </p>
                  </Button>
                </div>
              </div>
            </div>

            {/* Include our Table Component */}
            {decompositionGroups.length > 0 && (
              <div className="h-[calc(100%-104px)] overflow-y-auto scrollbar">
                <FindingOverviewTable
                  decompositionGroups={decompositionGroups}
                  setDecompositionGroups={setDecompositionGroups}
                  orderArray={orderArray}
                  setOrderArray={setOrderArray}
                  setRefresh={setRefresh}
                  groupedFindings={groupedFindings}
                  setGroupedFindings={setGroupedFindings}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default FindingOverview;
