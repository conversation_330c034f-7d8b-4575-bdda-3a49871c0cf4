import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { LoaderContextProvider } from "@/context/LoaderContext";
import { FindingContextProvider } from "@/context/findingContext";
import { SidebarContextProvider } from "@/context/sidebarContext";
// import '@/src/i18n';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Scalar Inspect",
  description: "Scalar Inspection Web",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <LoaderContextProvider>
        <FindingContextProvider>
          <SidebarContextProvider>
            <body className={inter.className}>{children}</body>
          </SidebarContextProvider>
        </FindingContextProvider>
      </LoaderContextProvider>
    </html>
  );
}
