import { fetchParentAPIValue } from "@/src/services/newFindings.api";
import { addFindingDetailsWithMedia, fetchAPIResponse, fetchTextSuggestions, generateFindings, updateFindingDetails, updateFinfingOrder } from "@/src/services/newInspection.api";
import { message } from "antd";
import { Timestamp } from "firebase/firestore";
import { createfieldvaluesObject } from "./utils";
import { mapFiledsWithValue } from "@/src/services/fieldsMapping";

export const prepareFinding = async (
  files: File[],
  parentDecompId: string,
  setProcessingFindingIds: any,
  setOrderArray: any,
  orderArray: any,
  decompositionGroups: any,
  setLoader: any,
  t: any,
  fetchFindings: any
) => {
  try {
    // Update processing state for this parent decomposition
    setProcessingFindingIds((prev: any) => ({
      ...prev,
      [parentDecompId]: (prev[parentDecompId] || 0) + 1,
    }));

    // Filter for only images and videos
    const mediaFiles = files.filter(
      (file) =>
        file.type.startsWith("image/") || file.type.startsWith("video/")
    );

    if (mediaFiles.length === 0) {
      message.error(t("Please select image or video files"));
      return;
    }

    let imagesArray: any = [];
    let videosArray: any = [];

    mediaFiles.forEach((file: any) => {
      const fileType = file.type.split("/")[0];
      if (fileType === "image") {
        imagesArray.push(file);
      } else if (fileType === "video") {
        videosArray.push(file);
      }
    });

    if (imagesArray.length === 0) {
      message.error(t("At least one image is required"));
      return;
    }

    const findingType = process.env.NEXT_FINDING_ID;
    const inspectionType = localStorage.getItem("reportTypeId");
    const inspection_id = localStorage.getItem("ScalarInspectionId");

    const res: any = await generateFindings(
      imagesArray,
      videosArray,
      setLoader,
      inspection_id,
      "finding",
      inspectionType,
      parentDecompId
    );

    if (res) {
      console.log('res', res)
      // const resize_media: any = localStorage.getItem("resize_media");
      let payload: any;
      if (res?.resize_media) {
        const image_urls = res?.resize_media.map((media: any) => media.url);
        payload = {
          image_urls: image_urls,
          parent_finding: parentDecompId,
          suggestions: {
            fields: res.suggestions.fields,
            finding_type: res.suggestions.finding_type,
            title_fields: res.suggestions.title_fields,
          },
        };
      } else {
        payload = {
          image_urls: [],
          parent_finding: parentDecompId,
          suggestions: {
            fields: res.suggestions.fields,
            finding_type: res.suggestions.finding_type,
            title_fields: res.suggestions.title_fields,
          },
        };
      }

      let fieldsAfterUpdate: any = res?.suggestions?.fields;

      const allTextFields = fieldsAfterUpdate
          .filter((field: any) => field.type === "text")

      const allTextFieldsNull = allTextFields
        .every((field: any) => field?.suggested_value === null);

      if (allTextFields.length > 0 && allTextFieldsNull) {
        const textSuggestionRes = await fetchTextSuggestions(
          inspectionType,
          inspection_id,
          "finding",
          payload
        );

        if (textSuggestionRes) {
          console.log('res', res)
          // const mappedTextFields = mapFiledsWithValue(fieldsAfterUpdate, textSuggestionRes.suggestions.fields)
          const fieldsAfterTextSuggestionUpdate =
            textSuggestionRes.suggestions.fields.map((field: any) => {
              if (
                field.type === "parent_api" &&
                field.suggested_value === null
              ) {
                return {
                  ...field,
                  suggested_value: 0,
                };
              } else {
                return field;
              }
            });
          fieldsAfterUpdate = fieldsAfterTextSuggestionUpdate;
        }
      }

      const apiField = fieldsAfterUpdate.find(
        (field: any) => field.type === "api"
      );

      if (apiField) {
        const input_fields = fieldsAfterUpdate
          .filter((field: any) =>
            apiField?.parameters?.input_fields.includes(field.id)
          )
          .map((field: any) => {
            return {
              field_id: field.id,
              value_id: field.suggested_value,
              type: field.type,
            };
          })
          .filter((field: any) => field !== undefined);
        const payload = {
          input_fields: input_fields,
        };

        const api_response = await fetchAPIResponse(
          inspectionType,
          inspection_id,
          "finding",
          payload
        );

        if (api_response) {
          const updatedFindings = fieldsAfterUpdate.map((field: any) => {
            if (field.type === "api") {
              return {
                ...field,
                suggested_value: api_response.result.value,
              };
            }
            return field;
          });

          fieldsAfterUpdate = updatedFindings;
        }
      }

      const parentAPIField = fieldsAfterUpdate.find(
        (field: any) => field.type === "parent_api"
      );
      if (parentAPIField) {
        const payload = {
          parent_finding_id: "noid",
        };
        const parentRes = await fetchParentAPIValue(
          inspectionType,
          inspection_id,
          "finding",
          parentAPIField?.id,
          payload
        );

        if (parentRes) {
          // const value = parseInt(parentRes?.result?.value);

          const updatedFields = fieldsAfterUpdate.map((field: any) => {
            if (field.type === "parent_api") {
              return {
                ...field,
                suggested_value: parentRes.result.value,
              };
            } else {
              return field;
            }
          });
          fieldsAfterUpdate = updatedFields;
        }
      }

      const clientId = localStorage.getItem("client");
      const final_fields = fieldsAfterUpdate.map((field: any) => {
        if (field.id === "image_field") {
          return {
            ...field,
            suggested_value: null,
          };
        } else return field;
      });
      const field_values = await createfieldvaluesObject(final_fields);
      const timeStamp = Timestamp.now();

      const updatedFields = final_fields.map((field: any) => {
        if (field.suggested_value === null) {
          return {
            ...field,
            suggested_value: "",
          };
        } else return field;
      });

      const findingDetails: any = {
        fields: JSON.stringify(updatedFields),
        title_fields: res.suggestions.title_fields,
        media: [],
        client: clientId,
        field_values: field_values,
        parent_finding_id: parentDecompId,
        created_at: timeStamp,
        updated_at: timeStamp,
        source: "web",
        approved: false,
        finding_type: { id: findingType },
      };

      const addFindingRes: any = await addFindingDetailsWithMedia(
        inspection_id,
        findingDetails,
        "finding",
        res?.resize_media,
        imagesArray
      );

      if (addFindingRes && addFindingRes.id) {
        const parentDecompFields = JSON.parse(
          decompositionGroups.find(
            (group: any) => group.id === parentDecompId
          )?.decomposition?.fields
        );

        const parentAPIField = parentDecompFields.find(
          (field: any) => field.type === "parent_api"
        );
        if (parentAPIField) {
          const payload = {
            parent_finding_id: parentDecompId,
          };
          const parentRes = await fetchParentAPIValue(
            inspectionType,
            inspection_id,
            "decomp",
            parentAPIField?.id,
            payload
          );

          if (parentRes) {
            // const value = parseInt(parentRes?.result?.value);

            const value = parseInt(parentRes?.result?.value);

            const updatedFields = parentDecompFields.map((field: any) => {
              if (field.type === "parent_api") {
                return {
                  ...field,
                  suggested_value: value,
                };
              } else {
                return field;
              }
            });

            const updatedFieldValues = await createfieldvaluesObject(
              updatedFields
            );

            const findingDetails: any = {
              fields: JSON.stringify(updatedFields),
              field_values: updatedFieldValues,
            };

            updateFindingDetails(findingDetails, parentDecompId);
          }
        }

        // Add the new finding to the orderArray
        setOrderArray((prev: any) => [
          ...prev,
          {
            id: addFindingRes.id,
            type: "finding",
            parentId: parentDecompId,
          },
        ]);

        // Update the order in localStorage
        const newOrderIds = [
          ...orderArray,
          {
            id: addFindingRes.id,
            type: "finding",
            parentId: parentDecompId,
          },
        ].map((item) => item.id);
        updateFinfingOrder(newOrderIds);
      }
    }
    await fetchFindings();
  } catch (error) {
    console.error("Error preparing finding:", error);
    message.error(t("Something went wrong, try again later!"));
  } finally {
    // Decrement the processing counter for this parent
    setProcessingFindingIds((prev: any) => ({
      ...prev,
      [parentDecompId]: Math.max(0, (prev[parentDecompId] || 1) - 1),
    }));
  }
};