"use client";
import React, { useEffect, useState, useRef } from "react";
import { <PERSON><PERSON>s, Open_Sans } from "next/font/google";
import LoadingCards from "./loadingCards";
import {
  updateFindingDetails,
  fetchFinding,
  fetchReportTypeId,
  handleUploadWhileEdit,
  fetchParentAPIValue,
} from "@/src/services/newFindings.api";
import { Timestamp } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { Button, message, Modal, Popconfirm, Skeleton } from "antd";
import { useFindingContext } from "@/context/findingContext";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import Media from "@/components/mediaModal/mediaModal";
import axios from "axios";
import Suggestions from "./suggestions";
import UploadBox from "@/components/newInspection/common/uploadBox";
import MediaList from "@/components/newInspection/common/media";
import { v4 as uuidv4 } from "uuid";
import {
  deleteFinding,
  getMeasures,
  updateFinfingOrder,
  fetchTextSuggestions
} from "@/src/services/newInspection.api";
import {
  generateFindingTitle,
  generateMeasureTitle,
} from "@/components/newInspection/utils";
import AddMeasureModal from "@/components/newInspection/addMeasureModal/addMeasureModal";
import EditMeasureModal from "@/components/newInspection/editMeasureModal/editMeasureModal";
import { handleUpload } from "@/src/services/inspectionDetails.api";
import { createDBFieldObj } from "@/src/services/fieldsMapping";

const poppins = Poppins({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const EditFindingModal = ({
  isModalOpen,
  setIsModalOpen,
  setAllGroups,
  allGroups,
  setOrderArray,
  orderArray,
  setRefresh,
  groupedFindings,
  setGroupedFindings,
}: any) => {
  const fileInputRef = useRef<any>(null);

  const router = useRouter();
  const { t } = useTranslation();
  const { setImgSource, setNewAddedId, wholeGroup } = useFindingContext();

  const [isUpdate, setIsUpdate] = useState(false);
  const [UpdateInspection, setUpdateInspection] = useState<any>(null);
  const [suggestionResponse, setSuggestionResponse] = useState<any>(null);
  const [editableFindings, setEditableFindings] = useState<any>([]);
  const [media, setMedia] = useState<any>([]);
  const [imagesArray, setImagesArray] = useState<any>([]);
  const [videosArray, setVideosArray] = useState<any>([]);
  const [reportTypeId, setReportTypeId] = useState<any>("");
  const [updateFindingId, setUpdateFindingId] = useState<any>("");

  const [showMedia, setShowMedia] = useState(false);
  const [mediaSource, setMediaSource] = useState("");
  const [mediaType, setMediaType] = useState("");

  const [loader, setLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [measureLoader, setMeasureLoader] = useState(false);
  const [deleteLoader, setDeleteLoader] = useState(false);
  const { isLoadingCard, setIsLoadingCard } = useFindingContext();
  const [textSuggestionLoader, setTextSuggestionLoader] = useState(false);

  const [isAddingMeasure, setIsAddingMeasure] = useState(false);
  const [isMeasureUpdate, setIsMeasureUpdate] = useState(false);
  const [urlsToDelete, setUrlsToDelete] = useState<
    { mediaIndex: number; isVideo: boolean; url: string; resize_url: string }[]
  >([]);

  const [originalFindings, setOriginalFindings] = useState<any>([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMeasureModalOpen, setIsAddMeasureModalOpen] = useState(false);
  const [isEditMeasureModalOpen, setIsEditMeasureModalOpen] = useState(false);
  const [selectedMeasure, setSelectedMeasure] = useState<any>(null);
  const [measures, setMeasures] = useState<any>([]);

  const [imageUrl, setImageUrl] = useState<any>("");
  const [fieldImage, setFieldImage] = useState<any>(null);

  // Add function to check if findings have changed
  const hasChanges = () => {
    if (editableFindings.length !== originalFindings.length) return true;

    // Check if media has changed
    if (media.length > 0) return true;
    if (urlsToDelete.length > 0) return true;

    return editableFindings.some((current: any, index: number) => {
      const original = originalFindings[index];
      return current.suggested_value !== original.suggested_value;
    });
  };

  const handleDivClick = () => {
    fileInputRef.current.click();
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      handleFileChange(files);
    }
  };

  const handleFileDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    handleFileChange(files);
  };

  const handleFileChange = (files: any) => {
    const renamedFiles = files.map((file: any) => {
      const fileType = file.type.split("/")[0];
      const newFileName =
        fileType === "image"
          ? `image_${uuidv4()}.${file.name.split(".").pop()}`
          : `video_${uuidv4()}.${file.name.split(".").pop()}`;
      return new File([file], newFileName, { type: file.type });
    });
    const images: any = [];
    const videos: any = [];
    const newMedia: any = [];

    renamedFiles.forEach((file: any) => {
      const reader = new FileReader();
      reader.onloadend = async () => {
        const fileType = file.type.split("/")[0];
        const fileObject = { type: fileType, src: reader.result };
        newMedia.push(fileObject);

        if (fileType === "image") {
          images.push(file);
        } else if (fileType === "video") {
          videos.push(file);
        }

        if (images.length + videos.length === files.length) {
          setMedia([...media, ...newMedia]);
          setImagesArray((prevImages: any) => [...prevImages, ...images]);
          setVideosArray((prevVideos: any) => [...prevVideos, ...videos]);
        }
      };
      reader.readAsDataURL(file);
    });
  };

  // Function for Update findings
  const getFinding = async (id: any, obj: any) => {
    try {
      setIsLoadingCard(true);
      const res: any = await fetchFinding(id);
      if (res) {
        setUpdateInspection(res);
        const variables = await JSON.parse(res.fields);

        const image_urls = res.media
          .filter((item: any) => !item.isVideo)
          .map((item: any) => (item?.resize_url ? item.resize_url : item.url));

        const apiRes = {
          image_urls: image_urls,
          parent_finding: res?.parent_finding_id,
          suggestions: {
            fields: variables,
            finding_type: res?.finding_type.id,
            title_fields: res?.title_fields,
          },
        };

        setSuggestionResponse(apiRes);

        if (variables) {
          setEditableFindings(variables);
          setOriginalFindings(JSON.parse(JSON.stringify(variables)));
          fetchMeasures();
        }
        setIsLoadingCard(false);
      } else {
        setIsLoadingCard(false);
      }
    } catch (error) {
      if (axios.isCancel(error)) {
        setIsLoadingCard(false);
      } else {
        message.error(t("Something went wrong, try again later!"));
      }
      setLoader(false);
      setIsLoadingCard(false);
    }
  };

  // Function for fetching reportTypeId
  const getReportTypeId = async (id: any) => {
    setLoader(true);
    const res: any = await fetchReportTypeId(id);
    if (res) {
      localStorage.setItem("reportTypeId", res);
      setReportTypeId(res);
      setLoader(false);
    } else {
      setLoader(false);
      message.error(t("Error fetching Inspection types."));
    }
  };

  const hasRunRef = useRef(false);

  useEffect(() => {
    localStorage.removeItem("resize_media");
    if (hasRunRef.current) return;

    hasRunRef.current = true;

    const language: any = localStorage.getItem("ScalarLanguage");
    handleChangeLanguage(language);
    localStorage.removeItem("ViewInspectionDetails");
    const update = localStorage.getItem("isFindingUpdate");
    const measureUpdate = localStorage.getItem("isMeasureUpdate");
    const findingId: any = localStorage.getItem("findingId");
    setUpdateFindingId(findingId);
    const inspection_id = localStorage.getItem("ScalarInspectionId");
    setIsLoadingCard(false);
    getReportTypeId(inspection_id);

    const measure = localStorage.getItem("isAddingMeasure");

    if (update === "true") {
      setIsUpdate(true);
      if (measureUpdate === "true") {
        setIsMeasureUpdate(true);
      } else {
        const obj: any = localStorage.getItem("updateFindingObj");
        const json = JSON.parse(obj);
        getFinding(findingId, json);
      }
    }

    if (measure !== "true") {
      setIsAddingMeasure(false);
    }
  }, []);

  const createfieldvaluesObject = (arr: any) => {
    const obj: any = {};

    arr.forEach((item: any) => {
      if (item.id && item.suggested_value !== undefined) {
        obj[item.name] = item.suggested_value;
      }
    });

    return obj;
  };

  useEffect(() => {
    setNewAddedId("");
    setImgSource(null);
  }, []);

  // Function for update existing finding in the database
  const handleEditFinding = async () => {
    // Check if required fields are filled
    const hasEmptyRequiredField = editableFindings.some(
      (field: any) =>
        field.required &&
        (field.suggested_value === null ||
          field.suggested_value === undefined ||
          field.suggested_value === "" ||
          (field.type === "multipicker" &&
            Array.isArray(field.suggested_value) &&
            field.suggested_value.length === 0))
    );

    if (hasEmptyRequiredField) {
      message.error(t("Please fill all required fields"));
      return;
    }
    const isMeasureUpdate = localStorage.getItem("isMeasureUpdate");

    if (isMeasureUpdate === "false") {
      const hasNoExistingImages = !UpdateInspection.media.some(
        (item: any) => !item.isVideo
      );
      const hasNoNewImages = !media.some((item: any) => item.type === "image");

      if (videosArray.length > 0 && hasNoNewImages) {
        message.error(t("At least one image is required!"));
        return;
      }
    }

    setLoading(true);

    const imageField = editableFindings.find((field: any) => field.type === "image_field");

    let newFields = [...editableFindings];

    if (imageField && fieldImage) {
      const url = await handleUpload(fieldImage, "finding");
      imageField.suggested_value = url;
      imageField.media = {
        url: url,
        resize_url: url,
        isVideo: false,
      }
      newFields = newFields.map((field: any) => {
        if (field.id === imageField.id) {
          return imageField;
        }
        return field;
      });
    }

    const field_values = await createfieldvaluesObject(newFields);
    const timeStamp = Timestamp.now();

    // Create base finding details
    const baseDetails = {
      fields: JSON.stringify(newFields),
      field_values: field_values,
      updated_at: timeStamp,
      approved: true,
    };

    // Add media-related fields only for non-measure updates
    const findingDetails =
      isMeasureUpdate === "true"
        ? baseDetails
        : {
            ...baseDetails,
            media: UpdateInspection.media,
          };

    try {
      const inspectionId: any = localStorage.getItem("ScalarInspectionId");
      const inspectionType: any = localStorage.getItem(
        "ScalarInspectionTypeId"
      );

      // Update the finding details
      const res: any = await updateFindingDetails(
        findingDetails,
        updateFindingId
      );
      if (res) {
        if (UpdateInspection?.parent_finding_id !== null) {
          await updateParentAPIField(
            UpdateInspection?.parent_finding_id,
            inspectionType,
            inspectionId
          );
        }

        // Handle media upload in background only for non-measure updates
        if (
          isMeasureUpdate === "false" &&
          !isAddingMeasure &&
          (imagesArray.length > 0 || videosArray.length > 0)
        ) {
          await uploadMediaInBackgroundWhileEditing(
            UpdateInspection,
            updateFindingId
          );

          const hasNoExistingImages = !UpdateInspection.media.some(
            (item: any) => !item.isVideo
          );
          if (hasNoExistingImages) {
            setNewAddedId(updateFindingId);
            const imagesToUse = media.filter((m: any) => m.type === "image");
            if (imagesToUse.length > 0) {
              setImgSource(imagesToUse[0].src);
            }
          }
        }
        const parentId = localStorage.getItem("parentId");
        setAllGroups((prev: any) =>
          prev.map((group: any) => {
            if (group.id === parentId) {
              const updatedFindings = group.findings.map((finding: any) => {
                if (finding.id === updateFindingId) {
                  return {
                    ...finding,
                    fields: JSON.stringify(newFields),
                    field_values: baseDetails.field_values,
                    updated_at: baseDetails.updated_at,
                    approved: true,
                  };
                } else {
                  return finding;
                }
              });
              return {
                ...group,
                findings: updatedFindings,
              };
            } else {
              return group;
            }
          })
        );
        setIsModalOpen(false);
        message.success(t("Successfully Updated!"));
        setUrlsToDelete([]);
      } else {
        throw new Error("Update failed");
      }
    } catch (error) {
      message.error(t("Something went wrong, try again later!"));
    } finally {
      setLoading(false);
    }
  };

  const uploadMediaInBackgroundWhileEditing = async (
    UpdateInspection: any,
    findingId: string
  ) => {
    try {
      const mediaToUpload = [...imagesArray, ...videosArray];
      const newMedia: any[] = await handleUploadWhileEdit(mediaToUpload);

      if (newMedia.length > 0) {
        // Combine existing media with new media
        const allMedia = [...UpdateInspection.media, ...newMedia];

        // Update finding with all media URLs
        const mediaDetails = {
          media: allMedia,
        };
        await updateFindingDetails(mediaDetails, findingId);
      }
    } catch (error) {
      console.error("Background upload failed:", error);
    }
  };

  const textareaRef: any = useRef(null);

  // Function for view media of update inspection
  const handleViewUpdateInspectionMedia = (src: any, isVideo: any) => {
    if (isVideo) {
      setMediaType("video");
      setMediaSource(src);
      setShowMedia(true);
    } else {
      setMediaType("img");
      setMediaSource(src);
      setShowMedia(true);
    }
  };

  // Function for view media of new inspection
  const handleViewMedia = (src: any, type: any) => {
    if (type === "image") {
      setMediaType("img");
      setMediaSource(src);
      setShowMedia(true);
    } else {
      setMediaType("video");
      setMediaSource(src);
      setShowMedia(true);
    }
  };

  const updateParentAPIField = async (
    parent_finding_id: string,
    inspectionType: string,
    inspection_id: string
  ) => {
    const res: any = await fetchFinding(parent_finding_id);
    if (res) {
      const findingType = res?.finding_type?.id;
      const fields = await JSON.parse(res.fields);

      if (fields) {
        const parentAPIField = fields.find(
          (field: any) => field.type === "parent_api"
        );

        if (parentAPIField) {
          const payload = {
            parent_finding_id: parent_finding_id,
          };
          const res = await fetchParentAPIValue(
            inspectionType,
            inspection_id,
            findingType,
            parentAPIField?.id,
            payload
          );

          if (res) {
            const value = parseInt(res?.result?.value);

            const updatedFields = fields.map((field: any) => {
              if (field.type === "parent_api") {
                return {
                  ...field,
                  suggested_value: value,
                };
              } else {
                return field;
              }
            });

            const updatedFieldValues = await createfieldvaluesObject(
              updatedFields
            );

            const findingDetails: any = {
              fields: JSON.stringify(updatedFields),
              field_values: updatedFieldValues,
            };

            await updateFindingDetails(findingDetails, parent_finding_id);
          }
        }
      }
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteLoader(true);
      const res: any = await deleteFinding(updateFindingId);

      if (res) {

        const inspectionId: any = localStorage.getItem("ScalarInspectionId");
        const inspectionType: any = localStorage.getItem(
          "ScalarInspectionTypeId"
        );

        await updateParentAPIField(
          UpdateInspection?.parent_finding_id,
          inspectionType,
          inspectionId
        );

        // 1. Remove the finding from its parent group
        setAllGroups((prev: any) =>
          prev.map((group: any) => {
            if (group.id === wholeGroup.id) {
              return {
                ...group,
                findings: group.findings.filter(
                  (finding: any) => finding.id !== updateFindingId
                ),
              };
            }
            return group;
          })
        );

        // 2. Remove the finding and its measures from orderArray
        setOrderArray((prev: any) => {
          // Remove finding and all its measures
          return prev
            .filter((item: any) => item.id !== updateFindingId)
            .filter((item: any) => item.parentId !== updateFindingId);
        });

        // 3. Update the order in localStorage and database
        const newOrderIds = orderArray
          .filter((item: any) => item.id !== updateFindingId)
          .filter((item: any) => item.parentId !== updateFindingId)
          .map((item: any) => item.id);

        localStorage.setItem("findingOrder", JSON.stringify(newOrderIds));
        updateFinfingOrder(newOrderIds);
        setRefresh((prev: any) => !prev);
        setIsModalOpen(false);
        message.success(t("Successfully Deleted!"));
      } else {
        message.error(t("Something went wrong, try again later!"));
      }
    } catch (error) {
      message.error(t("Something went wrong, try again later!"));
    } finally {
      setDeleteLoader(false);
    }
  };

  const getTextSuggestions = async (suggestionObj: any) => {
    const inspectionTypeId: any = localStorage.getItem("reportTypeId");
    const inspectionId: any = localStorage.getItem("ScalarInspectionId");
    const findingTypeId: any = sessionStorage.getItem("FindingTypeId");

    try {
      setTextSuggestionLoader(true);
      const res = await fetchTextSuggestions(
        inspectionTypeId,
        inspectionId,
        findingTypeId,
        suggestionObj
      );

      if (res) {
        const textNumberFields = res?.suggestions?.fields.filter(
          (field: any) => field.type === "text" || field.type === "number"
        );
        const fieldsAfterUpdate = suggestionObj.suggestions.fields.map(
          (field: any) => {
            const textNumField = textNumberFields.find(
              (f: any) => f.id === field.id
            );
            if (textNumField) {
              return {
                ...field,
                suggested_value: textNumField.suggested_value,
              };
            } else {
              return field;
            }
          }
        );
        setSuggestionResponse(res);
        setEditableFindings(fieldsAfterUpdate);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setTextSuggestionLoader(false);
    }
  };

  const fetchMeasures = async () => {
    try {
      const findingId: any = localStorage.getItem("findingId");
      setMeasureLoader(true);
      const res: any = await getMeasures(findingId);
      if (res) {
        setMeasures(res);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setMeasureLoader(false);
    }
  };

  const handleMeasureClick = (measure: any) => {
    setSelectedMeasure(measure);
    setIsEditMeasureModalOpen(true);
  };

  return (
    <>
      <Modal
        centered
        title={
          <h1 className="text-[20px] leading-[24px] font-[500] px-3">
            {isEditMode ? t("Edit Finding") : t("Finding")}
          </h1>
        }
        open={isModalOpen}
        onOk={isEditMode ? handleEditFinding : () => setIsModalOpen(false)}
        onCancel={() => setIsModalOpen(false)}
        okText={isEditMode ? t("Save") : "Close"}
        okButtonProps={{
          loading: loading,
          disabled: loader || isEditMode,
          className: "h-[40px] text-[16px] custom-button-disable",
        }}
        footer={[
          isEditMode ? (
            <Popconfirm
              key="delete"
              title={t("Delete finding")}
              placement="topRight"
              description={t("Are you sure you want to delete?")}
              onConfirm={handleDelete}
              okText={t("Yes")}
              cancelText={t("No")}
              okButtonProps={{
                loading: deleteLoader,
                danger: true,
              }}
            >
              <Button className="h-[40px] text-[16px] leading-[24px] font-[400] hover:text-[#F0142F] hover:border-[#FF7875] custom-cancel-button">
                {t("Delete")}
              </Button>
            </Popconfirm>
          ) : (
            <Button
              key="edit"
              onClick={() => setIsEditMode(true)}
              className="h-[40px] text-[16px] leading-[24px] font-[400]"
              disabled={loader}
            >
              {t("Edit")}
            </Button>
          ),
          <Button
            key="primary"
            type="primary"
            onClick={
              isEditMode ? handleEditFinding : () => setIsModalOpen(false)
            }
            loading={loading}
            disabled={loader || (isEditMode && !hasChanges())}
            className="h-[40px] text-[16px] custom-button-disable"
          >
            {isEditMode ? t("Save") : t("Close")}
          </Button>,
        ]}
        width="80vw"
      >
        <div
          className={`h-[72vh] text-black overflow-y-auto scrollbar mt-8 ${poppins.className}`}
        >
          <div className="mx-4">
            <div className="w-full flex gap-4 flex-wrap">
              {isLoadingCard ? (
                <div className="w-[90px] h-[90px] rounded-[15px]">
                  <Skeleton.Image
                    active
                    style={{ width: 90, height: 90, borderRadius: 15 }}
                  />
                </div>
              ) : (
                <MediaList
                  {...{
                    isUpdate,
                    UpdateInspection,
                    handleViewUpdateInspectionMedia,
                    media,
                    handleViewMedia,
                    urlsToDelete,
                    setUrlsToDelete,
                    setUpdateInspection,
                    setMedia,
                    setImagesArray,
                    setVideosArray,
                    imagesArray,
                    videosArray,
                    isEditMode,
                  }}
                />
              )}
              {!isAddingMeasure && !isMeasureUpdate && isEditMode && (
                <UploadBox
                  {...{
                    handleDivClick,
                    fileInputRef,
                    handleFileSelect,
                    handleFileDrop,
                    isUpdate,
                    t,
                    isLoadingCard,
                    loading,
                  }}
                />
              )}
            </div>
          </div>
          <div className="my-4 mx-4 flex flex-col box-border">
            {isLoadingCard ? (
              <LoadingCards />
            ) : (
              <Suggestions
                {...{
                  editableFindings,
                  setEditableFindings,
                  textareaRef,
                  isAddingMeasure,
                  isMeasureUpdate,
                  isUpdate,
                  suggestionResponse,
                  setSuggestionResponse,
                  textSuggestionLoader,
                  getTextSuggestions,
                  isEditMode,
                  fieldImage,
                  setFieldImage,
                  imageUrl,
                  setImageUrl,
                }}
              />
            )}
          </div>
          <div className="flex flex-col gap-4 ml-4">
            {isLoadingCard ? (
              <Skeleton.Input
                style={{ width: 60, height: 30, borderRadius: 10 }}
                active
              />
            ) : (
              <h1 className="text-[16px] leading-[24px] font-[500]">
                {t("Measures")}
              </h1>
            )}

            <div className="flex gap-2 overflow-x-auto pr-2 scrollbar mb-2">
              <>
                {measures.length > 0 &&
                  measures.map((measure: any) => (
                    <div
                      key={measure.id}
                      onClick={() => handleMeasureClick(measure)}
                      className={`w-[120px] h-[60px] flex-shrink-0 border border-[#2f81eda7] bg-[#2F80ED0D] rounded-[15px] hover:bg-[#2f81ed21] cursor-pointer text-center p-2 transition-all flex flex-col justify-center items-center break-all text-[12px]`}
                    >
                      <p>
                        {(() => {
                          const title = generateMeasureTitle(
                            measure?.title_fields,
                            measure?.fields,
                            0,
                            wholeGroup.decompositionItem.find(
                              (item: any) => item.id === updateFindingId
                            )
                          );
                          return title.length > 30
                            ? title.slice(0, 30) + "..."
                            : title;
                        })()}
                      </p>
                    </div>
                  ))}
                {!isEditMode &&
                  measures.length === 0 &&
                  !isLoadingCard &&
                  !measureLoader && (
                    <p className="text-[14px] leading-[24px] font-[400]">
                      {t("No measures added yet.")}
                    </p>
                  )}
                {(isLoadingCard || measureLoader) && (
                  <div className="w-[80px]">
                    <Skeleton.Input
                      style={{ width: "100%", height: 60, borderRadius: 15 }}
                      active
                    />
                  </div>
                )}
                {isEditMode && !measureLoader && (
                  <div
                    className="w-[120px] h-[60px] flex-shrink-0 border-2 border-dashed border-orange-300 bg-orange-50 rounded-[15px] cursor-pointer hover:bg-orange-100 transition-all flex flex-col justify-center items-center"
                    onClick={() => setIsAddMeasureModalOpen(true)}
                  >
                    {/* <div className="flex justify-center items-center text-[22px] border-2 border-orange-400 text-orange-400 w-[25px] h-[25px] rounded-full pt-[2px] mb-2 mx-auto">
                        +
                      </div> */}
                    <p
                      className={`text-[12px] text-center text-orange-500 font-[500] px-3 ${OpenSans.className}`}
                    >
                      {t("Add Measure")}
                    </p>
                  </div>
                )}
              </>
            </div>
          </div>
        </div>
      </Modal>
      <Media
        mediaType={mediaType}
        source={mediaSource}
        showMedia={showMedia}
        setShowMedia={setShowMedia}
      />
      {isAddMeasureModalOpen && (
        <AddMeasureModal
          isModalOpen={isAddMeasureModalOpen}
          setIsModalOpen={setIsAddMeasureModalOpen}
          parent_finding_id={updateFindingId}
          isFindingUpdate={false}
          isAddingMeasure={true}
          fetchMeasures={fetchMeasures}
          setGroupedFindings={setGroupedFindings}
          setOrderArray={setOrderArray}
          orderArray={orderArray}
          setRefresh={(value: any) => setRefresh(value)}
          groupedFindings={groupedFindings}
          setFields={setEditableFindings}
          parentSuggetion={suggestionResponse}
          setSuggestion={setSuggestionResponse}
        />
      )}
      {isEditMeasureModalOpen && (
        <EditMeasureModal
          isModalOpen={isEditMeasureModalOpen}
          setIsModalOpen={setIsEditMeasureModalOpen}
          parent_finding_id={updateFindingId}
          isFindingUpdate={true}
          isAddingMeasure={false}
          isMeasureUpdate={true}
          fetchMeasures={fetchMeasures}
          setGroupedFindings={setGroupedFindings}
          setOrderArray={setOrderArray}
          orderArray={orderArray}
          setRefresh={(value: any) => setRefresh(value)}
          groupedFindings={groupedFindings}
          setMeasures={setMeasures}
          selectedMeasure={selectedMeasure}
          setAllGroups={setAllGroups}
        />
      )}
    </>
  );
};

export default EditFindingModal;
