import Image from "next/image";
import React from "react";
import { Open_Sans } from "next/font/google";

const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const UploadBox = ({
  handleDivClick,
  fileInputRef,
  handleFileSelect,
  handleFileDrop,
  isUpdate,
  t,
  isLoadingCard,
  loading,
}: any) => {
  return (
    <div
      className={`w-[90px] h-[90px] border-2 border-dashed border-orange-300 bg-orange-50 ${
        isLoadingCard || loading
          ? "cursor-not-allowed"
          : "cursor-pointer hover:bg-orange-100"
      } transition-all rounded-[15px] flex justify-center items-center cursor-pointer`}
      onClick={handleDivClick}
      onDrop={(e: any) => handleFileDrop(e)}
      onDragOver={(e) => e.preventDefault()}
    >
      <div className="text-center">
      <div className="flex justify-center items-center text-[14px] border-2 border-orange-400 text-orange-400 w-[20px] h-[20px] rounded-full pt-[2px] pl-[1px] mb-2 mx-auto">
          +
        </div>
        <p
          className={`text-[8px] text-orange-500 font-[500] px-3 ${OpenSans.className}`}
        >
          {t("Drop image or click here")}
        </p>
      </div>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileSelect}
        accept="image/*,video/*"
        className="hidden"
        placeholder="s"
        disabled={isLoadingCard || loading}
        multiple
      />
    </div>
  );
};

export default UploadBox;
