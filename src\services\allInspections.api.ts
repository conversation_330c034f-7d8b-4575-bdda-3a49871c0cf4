import { db } from "@/firebase.config";
import {
  writeBatch,
  doc,
  getDoc,
  arrayRemove,
  query,
  collection,
  where,
  orderBy,
  limit,
  startAfter,
  getDocs,
} from "firebase/firestore";

export const deleteInspections = async (
  selectedInspections: { id: string; project: { id: string } }[]
): Promise<boolean> => {
  try {
    const batch = writeBatch(db);

    for (const inspection of selectedInspections) {
      const {
        id: inspectionId,
        project: { id: projectId },
      } = inspection;

      // Get the project document reference
      const projectDocRef = doc(db, "projects", projectId);

      // Verify project exists
      const projectSnapshot = await getDoc(projectDocRef);
      if (!projectSnapshot.exists()) {
        continue; // Skip this inspection
      }

      const inspectionReference = doc(db, "inspection", inspectionId);

      // Add project update to the batch
      batch.update(projectDocRef, {
        inspections: arrayRemove(inspectionReference),
      });

      // Add inspection deletion to the batch
      batch.delete(inspectionReference);
    }

    // Execute all operations atomically
    await batch.commit();

    return true;
  } catch (error) {
    throw error;
  }
};

export const getInspections = async ({
  search = "",
  isForFirst = true,
  lastDocument = null,
  limitValue = 10,
}: any) => {
  try {
    const clientId = localStorage.getItem("client");
    let inspectionList: any = [];
    let hasMore = false;

    // Base query
    let queryRef;

    if (search) {
      // Query for search functionality with pagination
      queryRef = query(
        collection(db, "inspection"),
        where("client", "==", clientId),
        where("name", ">=", search),
        where("name", "<=", `${search}\uf8ff`),
        orderBy("name"), // Use orderBy("name") because of range query
        orderBy("created_at", "desc"),
        limit(limitValue)
      );

      if (!isForFirst && lastDocument) {
        queryRef = query(
          collection(db, "inspection"),
          where("client", "==", clientId),
          where("name", ">=", search),
          where("name", "<=", `${search}\uf8ff`),
          orderBy("name"),
          orderBy("created_at", "desc"),
          startAfter(lastDocument),
          limit(limitValue)
        );
      }
    } else {
      // Query for standard project fetching with pagination
      queryRef = query(
        collection(db, "inspection"),
        where("client", "==", clientId),
        orderBy("created_at", "desc"),
        limit(limitValue)
      );

      if (!isForFirst && lastDocument) {
        queryRef = query(
          collection(db, "inspection"),
          where("client", "==", clientId),
          orderBy("created_at", "desc"),
          startAfter(lastDocument),
          limit(limitValue)
        );
      }
    }

    // Fetch data
    const querySnapshot = await getDocs(queryRef);

    // Check if there are more documents
    hasMore = querySnapshot.docs.length === limitValue;

    // Map through documents
    if (!querySnapshot.empty) {
      inspectionList = querySnapshot.docs.map((doc) => {
        return { ...doc.data(), id: doc.id }; // Map to match ProjectModel/InspectionModel equivalent
      });
    }

    return {
      list: inspectionList,
      hasMore,
      lastDocument:
        querySnapshot.docs.length > 0
          ? querySnapshot.docs[querySnapshot.docs.length - 1]
          : null,
    };
  } catch (error) {
    console.error('inspectionError', error)
  }
};

export const getProjectNames = async (projectIds: string[]) => {
  try {
    const promises = projectIds.map(async (id) => {
      try {
        const ref = doc(db, "projects", id);
        const snap = await getDoc(ref);
        if (snap.exists()) {
          const data = snap.data();
          return [id, data?.name ?? data?.display_name ?? "-"] as [string, string];
        } else {
          return [id, "-"] as [string, string];
        }
      } catch (err) {
        console.error(`Error fetching project with id ${id}:`, err);
        return null; // Skip this id
      }
    });

    const results = await Promise.all(promises);
    const filtered = results.filter((item): item is [string, string] => !!item);

    return Object.fromEntries(filtered);
  } catch (error) {
    console.error('projectError', error);
  }
};
