import React from "react";
import css from "@/components/inspectionMetaData/inspectionMetaData.module.css";

const LoadingCards = () => {
  return (
    <div className="mt-2 mb-6 flex flex-col gap-4">
      <div className="bg-white w-full p-4 box-border border border-[#B4B4B44D] rounded-[20px]">
        <h1
          className={`flex justify-center text-[16px] leading-[16px] font-[500] text-center `}
        >
          <p
            className={`w-[60%] h-[16px] bg-[#f5f5f5] rounded-lg ${css.shimmer}`}
          ></p>
        </h1>
        <div
          className={`text-[12px] leading-[12px] font-[500] text-[#B4B4B4] mt-5 mb-6 `}
        >
          <p
            className={`w-[30%] h-[12px] bg-[#f5f5f5] rounded-lg ${css.shimmer}`}
          ></p>
        </div>
      </div>
      <div className="bg-white w-full p-4 box-border border border-[#B4B4B44D] rounded-[20px]">
        <h1
          className={`flex justify-center text-[16px] leading-[16px] font-[500] text-center `}
        >
          <p
            className={`w-[60%] h-[16px] bg-[#f5f5f5] rounded-lg ${css.shimmer}`}
          ></p>
        </h1>
        <div
          className={`text-[12px] leading-[12px] font-[500] text-[#B4B4B4] mt-5 mb-6 `}
        >
          <p
            className={`w-[30%] h-[12px] bg-[#f5f5f5] rounded-lg ${css.shimmer}`}
          ></p>
        </div>
      </div>
    </div>
  );
};

export default LoadingCards;
