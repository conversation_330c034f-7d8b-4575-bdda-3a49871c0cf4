import { auth, db, refreshToken } from "@/firebase.config";
import {
    signInWithEmailAndPassword,
    sendPasswordResetEmail,
    fetchSignInMethodsForEmail,
    updatePassword,
    getAuth,
    signInWithPopup,
    GoogleAuthProvider,
    OAuthProvider,
    signOut,
    signInWithRedirect,
    reauthenticateWithPopup,
    linkWithCredential,
    linkWithPopup,
} from "firebase/auth";
import {
    collection,
    getDocs,
    doc,
    getDoc,
    query,
    where,
    updateDoc,
} from "firebase/firestore";
import { message } from "antd";
// import firebase from 'firebase/app';
// import 'firebase/auth';

export const signInWithFirebase = async (
    email: string,
    password: string
): Promise<any> => {
    const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
    );
    const idToken = await userCredential.user.getIdToken();
    return { userCredential, idToken };
};

let tokenRefreshInterval: any = null;

export const loginWithGoogle = async () => {
    try {
        const provider = new GoogleAuthProvider();
        const result = await signInWithPopup(auth, provider);
        return result.user;
    } catch (error) {
        console.log('error', error)
    }
};

export const loginWithMicrosoft = async () => {
    try {
        const provider = new OAuthProvider("microsoft.com");
        const result = await signInWithPopup(auth, provider);
        return result.user;
    } catch (error: any) {
        if (error.code === "auth/account-exists-with-different-credential") {
            const email = error.customData?.email;


            if (email) {
                // Fetch the sign-in methods for the existing email
                const signInMethods = await fetchSignInMethodsForEmail(auth, email);

                // console.log("signInMethods", signInMethods);
                alert("It seems like your email is already linked to a Google account. Please sign in with Google to link your accounts.");

                // Wait for user action to trigger the second popup
                const googleProvider = new GoogleAuthProvider();
                const result = await signInWithPopup(auth, googleProvider);
                console.log('result', result)

                // Link the new credential to the existing account
                const provider = new OAuthProvider("microsoft.com");
                await linkWithPopup(result.user, provider);
                console.log("Account linked successfully.");
            }
        } else {
            console.error("Error during login:", error.message);
        }
    }
};

export const loginWithApple = async () => {
    try {
        const auth: any = getAuth();
        const provider = new OAuthProvider("apple.com");
        const result: any = await signInWithPopup(auth, provider);

        // The signed-in user info.
        const user = result.user;

        // Apple credential
        const credential: any = OAuthProvider.credentialFromResult(result);
        const accessToken = credential.accessToken;
        const idToken = credential.idToken;
        return { user, idToken };
    } catch (error) {
        console.error('error', error)
    }
};

export const getUserReportTypes = async (userId: any) => {
    try {
        const docRef = doc(db, "user", userId);

        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            const document = await docSnap.data();

            // const collectionRef = collection(db, "user");
            // const condition = query(collectionRef, where("firebase_uid", "==", userId));
            // const querySnapshot = await getDocs(condition);

            // const documents = querySnapshot.docs.map((doc: any) => ({
            //     id: doc.id,
            //     ...doc.data(),
            // }));

            // if (documents.length === 0) {
            //     throw new Error("User not found.");
            // }

            const reportTypesArray = document?.allowed_reporttypes;
            if (!reportTypesArray) {
                throw new Error("No allowed report types found for the user.");
            }

            const reportTypes = await Promise.all(
                reportTypesArray.map(async (item: any) => {
                    const docRef = doc(db, "report_type", item);
                    const docSnap = await getDoc(docRef);

                    if (docSnap.exists()) {
                        const docData = docSnap.data();
                        return { value: item, label: docData.name };
                    }
                    return null;
                })
            );

            const inspectionTypes = reportTypes.filter((item) => item !== null);
            localStorage.setItem("inspectionTypes", JSON.stringify(inspectionTypes));

            const res = {
                inspectionTypes: inspectionTypes,
                userId: userId,
                isTermsAccepted: document.isTermsAccepted,
            };

            return res; // Return the resolved data to the caller
        } else {
            throw new Error("User not found.");
        }
    } catch (error) {
        throw error; // Throw the error if needed for handling in the calling function
    }
};

export const signOutUser = async (router: any) => {
    try {
        await signOut(auth);
        localStorage.clear();
        router.push("/");
        return true;
    } catch (error) {
        return false;
    }
};

export const forgotPasswordWithFirebase = async (
    email: string
): Promise<any> => {
    try {
        await sendPasswordResetEmail(auth, email);
        return true;
    } catch (error) {
        return false;
    }
};

export const updateUserTermsFlag = async (userId: any) => {
    try {
        const docRef = doc(db, "user", userId);

        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            await updateDoc(docRef, { isTermsAccepted: true });
            return true;
        } else {
            throw new Error("User not found");
        }
    } catch (error) {
        throw error;
    }
};
