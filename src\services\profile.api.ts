import { getAuth, onAuthStateChanged } from "firebase/auth";

const auth = getAuth();

export async function fetchUserDetails() {
  try {
    const user = await new Promise((resolve, reject) => {
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        unsubscribe(); // Unsubscribe from the listener once we get the user
        if (user) {
          resolve(user);
        } else {
          reject("No user is signed in.");
        }
      });
    });
    return user
  } catch (error) {
  }
}
