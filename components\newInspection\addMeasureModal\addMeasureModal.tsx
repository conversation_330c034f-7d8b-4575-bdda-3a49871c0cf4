"use client";
import React, { useEffect, useState, useRef } from "react";
import { Poppins, Open_Sans } from "next/font/google";
import LoadingCards from "./loadingCards";
import {
  generateMeasureFindings,
  addFindingDetails,
  fetchFinding,
  fetchReportTypeId,
  updateFindingDetails,
  fetchParentAPIValue,
} from "@/src/services/newFindings.api";
import { handleUpload } from "@/src/services/inspectionDetails.api";
import { Timestamp } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { Button, message, Modal } from "antd";
import { useFindingContext } from "@/context/findingContext";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/context/LanguageContext";
import Media from "@/components/mediaModal/mediaModal";
import axios from "axios";
import Suggestions from "./suggestions";
import { v4 as uuidv4 } from "uuid";
import { updateFinfingOrder, fetchTextSuggestions } from "@/src/services/newInspection.api";

const poppins = Poppins({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const OpenSans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const AddMeasureModal = ({
  isModalOpen,
  setIsModalOpen,
  parent_finding_id,
  isAddingMeasure,
  fetchMeasures,
  setGroupedFindings,
  orderArray,
  setOrderArray,
  setRefresh,
  setFields,
  setSuggestion,
  parentSuggetion,
}: any) => {
  const router = useRouter();
  const { t } = useTranslation();
  const { setImgSource, setNewAddedId } = useFindingContext();

  const [suggestionResponse, setSuggestionResponse] = useState(null);
  const [editableFindings, setEditableFindings] = useState<any>([]);
  const [titleFields, setTitleFields] = useState<any>([]);
  const [reportTypeId, setReportTypeId] = useState<any>("");

  const [showMedia, setShowMedia] = useState(false);
  const [mediaSource, setMediaSource] = useState("");
  const [mediaType, setMediaType] = useState("");

  const [imageUrl, setImageUrl] = useState<any>("");
  const [fieldImage, setFieldImage] = useState<any>(null);

  const [loader, setLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [apiLodaer, setApiLodaer] = useState(false);
  // const { isLoadingCard, setIsLoadingCard } = useFindingContext();
  const [isLoadingCard, setIsLoadingCard] = useState(true);
  const [textSuggestionLoader, setTextSuggestionLoader] = useState(false);

  // Function for fetching reportTypeId
  const getReportTypeId = async (id: any) => {
    setLoader(true);
    const res: any = await fetchReportTypeId(id);
    if (res) {
      localStorage.setItem("reportTypeId", res);
      setReportTypeId(res);
      setLoader(false);
    } else {
      setLoader(false);
      message.error(t("Error fetching Inspection types."));
    }
  };

  function separateImagesAndVideos(data: any) {
    const imageUrls: any = [];
    const videoUrls: any = [];

    data.forEach((item: any) => {
      if (item.isVideo) {
        videoUrls.push(item.url);
      } else {
        imageUrls.push(item.url);
      }
    });

    return {
      image_urls: imageUrls,
      video_urls: videoUrls,
    };
  }

  const cancelTokenSourceRef = useRef<any>(null);

  // Function for Generate New Measures
  const generateMeasures = async () => {
    try {
      const decompositionItemMedia: any = localStorage.getItem(
        "decompositionItemMedia"
      );
      const parsedMedia = JSON.parse(decompositionItemMedia);
      const rowData = separateImagesAndVideos(parsedMedia);
      const findingTypeId: any = process.env.NEXT_MEASURE_ID;
      const inspectionType = localStorage.getItem("ScalarInspectionTypeId");
      const inspection_id = localStorage.getItem("ScalarInspectionId");

      // Cancel the previous request if it exists
      if (cancelTokenSourceRef.current) {
        cancelTokenSourceRef.current.cancel("Operation canceled by the user.");
      }

      // Create a new CancelToken for the new request
      cancelTokenSourceRef.current = axios.CancelToken.source();

      const res = await generateMeasureFindings(
        setLoader,
        inspection_id,
        findingTypeId,
        inspectionType,
        rowData,
        cancelTokenSourceRef.current.token,
        setIsLoadingCard,
        setTitleFields
      );

      if (res) {
        const textFields = res?.suggestions?.fields.filter(
          (field: any) => field.type === "text"
        );

        if (textFields.length > 0) {
          const allTextFieldsNull = res?.suggestions?.fields
            .filter((field: any) => field.type === "text")
            .every((field: any) => field.suggested_value === null);
          if (allTextFieldsNull) {
            getTextSuggestions(res);
          }
        }
        setSuggestionResponse(res);
        setEditableFindings(res.suggestions.fields);
      }
    } catch (error) {
      setIsLoadingCard(false);
      if (axios.isCancel(error)) {
        setIsLoadingCard(true);
      } else {
        message.error(t("Something went wrong, try again later!"));
      }
      setLoader(false);
      return false;
    }
  };

  const hasRunRef = useRef(false);

  useEffect(() => {
    localStorage.removeItem("resize_media");
    if (hasRunRef.current) return; // Prevent the effect from running again

    hasRunRef.current = true; // Mark as run

    const laguage: any = localStorage.getItem("ScalarLanguage");
    handleChangeLanguage(laguage);
    localStorage.removeItem("ViewInspectionDetails");
    const inspection_id = localStorage.getItem("ScalarInspectionId");
    setIsLoadingCard(false);
    getReportTypeId(inspection_id);

    if (isAddingMeasure) {
      generateMeasures();
    }
  }, []);

  const getTextSuggestions = async (suggestionObj: any) => {
    const inspectionTypeId: any = localStorage.getItem("reportTypeId");
    const inspectionId: any = localStorage.getItem("ScalarInspectionId");
    const findingTypeId: any = process.env.NEXT_MEASURE_ID;
    const parentFindingId: any = parent_finding_id;
    const resize_media: any = localStorage.getItem("resize_media");
    const measure = localStorage.getItem("isAddingMeasure");
    let payload: any;

    if (measure === "true") {
      const decompositionItemMedia: any = localStorage.getItem(
        "decompositionItemMedia"
      );
      const parsedMedia = JSON.parse(decompositionItemMedia);
      const rowData = separateImagesAndVideos(parsedMedia);
      payload = {
        image_urls: rowData.image_urls,
        suggestions: suggestionObj.suggestions,
      };
    } else {
      if (resize_media) {
        const image_urls = JSON.parse(resize_media).map(
          (media: any) => media.url
        );
        payload = {
          image_urls: image_urls,
          parent_finding: parentFindingId,
          suggestions: suggestionObj.suggestions,
        };
      } else {
        payload = {
          image_urls: [],
          parent_finding: parentFindingId,
          suggestions: suggestionObj.suggestions,
        };
      }
    }

    try {
      setTextSuggestionLoader(true);
      const res = await fetchTextSuggestions(
        inspectionTypeId,
        inspectionId,
        findingTypeId,
        payload
      );

      if (res) {
        const textNumberFields = res?.suggestions?.fields.filter(
          (field: any) => field.type === "text" || field.type === "number"
        );
        const fieldsAfterUpdate = suggestionObj.suggestions.fields.map(
          (field: any) => {
            const textNumField = textNumberFields.find(
              (f: any) => f.id === field.id
            );
            if (textNumField) {
              return {
                ...field,
                suggested_value: textNumField.suggested_value,
              };
            } else {
              return field;
            }
          }
        );
        setSuggestionResponse(res);
        setEditableFindings(fieldsAfterUpdate);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setTextSuggestionLoader(false);
    }
  };

  const createfieldvaluesObject = (arr: any) => {
    const obj: any = {};

    arr.forEach((item: any) => {
      if (item.id && item.suggested_value !== undefined) {
        obj[item.name] = item.suggested_value;
      }
    });

    return obj;
  };

  useEffect(() => {
    setNewAddedId("");
    setImgSource(null);
  }, []);

  const handleAddFinding = async () => {
    const hasEmptyRequiredField = editableFindings.some(
      (field: any) =>
        field.required &&
        (field.suggested_value === null ||
          field.suggested_value === undefined ||
          field.suggested_value === "" ||
          (field.type === "multipicker" &&
            Array.isArray(field.suggested_value) &&
            field.suggested_value.length === 0))
    );

    if (hasEmptyRequiredField) {
      message.error(t("Please fill all required fields"));
      return;
    }

    setLoading(true);
    const inspection_id: any = localStorage.getItem("ScalarInspectionId");
    const clientId = localStorage.getItem("client");
    const timeStamp = Timestamp.now();
    const findingTypeId: any = process.env.NEXT_MEASURE_ID;

    const imageField = editableFindings.find(
      (field: any) => field.type === "image_picker"
    );

    let newFields = [...editableFindings];

    if (imageField && fieldImage) {
      const url = await handleUpload(fieldImage, "finding");
      imageField.suggested_value = url;
      imageField.media = {
        url: url,
        resize_url: url,
        isVideo: false,
      };
      newFields = newFields.map((field: any) => {
        if (field.id === imageField.id) {
          return imageField;
        }
        return field;
      });
    }

    const fieldsString = JSON.stringify(newFields);
    const field_values = await createfieldvaluesObject(newFields);

    const findingDetails: any = {
      fields: fieldsString,
      title_fields: titleFields,
      media: [],
      client: clientId,
      ...(field_values && { field_values: field_values }),
      ...(parent_finding_id === "null"
        ? { parent_finding_id: null }
        : { parent_finding_id: parent_finding_id }),
      ...(timeStamp && { created_at: timeStamp }),
      ...(timeStamp && { updated_at: timeStamp }),
      approved: true,
      source: "web",
    };

    try {
      // Add finding details
      const res: any = await addFindingDetails(
        inspection_id,
        findingDetails,
        findingTypeId
      );

      if (res && res.id) {
        if (parent_finding_id !== "null") {
          const inspectionType: any = localStorage.getItem(
            "ScalarInspectionTypeId"
          );
          await updateParentAPIField(
            parent_finding_id,
            inspectionType,
            inspection_id
          );
        }

        // Update groupedFindings first
        setGroupedFindings((prev: any) => {
          const groupId = localStorage.getItem("parentId");
          return prev.map((item: any) => {
            if (item.id === groupId) {
              return {
                ...item,
                decompositionItem: [...item.decompositionItem, res],
              };
            }
            return item;
          });
        });

        // Get the current order from localStorage to ensure we have the latest
        const currentOrderStr = localStorage.getItem("findingOrder");
        let currentOrder = currentOrderStr ? JSON.parse(currentOrderStr) : [];

        // Find the parent in the order
        const parentIndex = currentOrder.indexOf(parent_finding_id);

        // Find the index after the last child of this parent
        let insertIndex = parentIndex + 1;
        while (
          insertIndex < currentOrder.length &&
          orderArray.find(
            (item: any) =>
              item.id === currentOrder[insertIndex] &&
              item.parentId === parent_finding_id
          )
        ) {
          insertIndex++;
        }

        // Insert the new measure ID at the right position
        currentOrder.splice(insertIndex, 0, res.id);

        // Save back to localStorage
        localStorage.setItem("findingOrder", JSON.stringify(currentOrder));

        // Update the order in the database
        updateFinfingOrder(currentOrder);

        // Force a refresh of the parent component
        setRefresh((prev: any) => !prev);

        message.success(t("Successfully added!"));
        fetchMeasures();
        setIsModalOpen(false);

        // Start background process to upload media
        if (isAddingMeasure) {
          setNewAddedId(res.id);
          setImgSource(null);
        }
      } else {
        throw new Error("Adding finding failed");
      }
    } catch (error) {
      setLoading(false);
      message.error(t("Something went wrong, try again later!"));
    } finally {
      setLoading(false);
      localStorage.removeItem("isAddingMeasure");
    }
  };

  const updateParentAPIField = async (
    parent_finding_id: string,
    inspectionType: string,
    inspection_id: string
  ) => {
    const res: any = await fetchFinding(parent_finding_id);
    if (res) {
      const findingType = res?.finding_type?.id;
      const fields = await JSON.parse(res.fields);

      if (fields) {
        const parentAPIField = fields.find(
          (field: any) => field.type === "parent_api"
        );

        if (parentAPIField) {
          const payload = {
            parent_finding_id: parent_finding_id,
          };
          const res = await fetchParentAPIValue(
            inspectionType,
            inspection_id,
            findingType,
            parentAPIField?.id,
            payload
          );

          if (res) {
            const value = parseInt(res?.result?.value);

            const updatedFields = fields.map((field: any) => {
              if (field.type === "parent_api") {
                return {
                  ...field,
                  suggested_value: value,
                };
              } else {
                return field;
              }
            });

            const updatedFieldValues = await createfieldvaluesObject(
              updatedFields
            );

            const findingDetails: any = {
              fields: JSON.stringify(updatedFields),
              field_values: updatedFieldValues,
            };

            const updateRes: any = await updateFindingDetails(
              findingDetails,
              parent_finding_id
            );

            if (updateRes) {
              setFields((prev: any) =>
                prev.map((field: any) => {
                  if (field.type === "parent_api") {
                    return {
                      ...field,
                      suggested_value: value,
                    };
                  } else {
                    return field;
                  }
                })
              );
              setSuggestion((prev: any) => ({
                ...prev,
                fields: parentSuggetion.suggestions.fields.map((field: any) => {
                  if (field.type === "parent_api") {
                    return {
                      ...field,
                      suggested_value: value,
                    };
                  } else {
                    return field;
                  }
                }),
              }));
            }
          }
        }
      }
    }
  };

  return (
    <>
      <Modal
        centered
        title={
          <h1 className="text-[20px] leading-[24px] font-[500] px-3">
            {t("Add Measure")}
          </h1>
        }
        open={isModalOpen}
        onOk={handleAddFinding}
        onCancel={() => setIsModalOpen(false)}
        okText="Approve"
        okButtonProps={{
          loading: loading,
          disabled: loader,
          className: "h-[40px] text-[16px]",
        }}
        footer={[
          <Button
            key="cancel"
            className="h-[40px] text-[16px] leading-[24px] font-[400] hover:text-[#F0142F] hover:border-[#FF7875] custom-cancel-button"
            disabled={loader}
            onClick={() => setIsModalOpen(false)}
          >
            {t("Cancel")}
          </Button>,
          <Button
            key="approve"
            onClick={handleAddFinding}
            loading={loading}
            disabled={loader || textSuggestionLoader || apiLodaer || editableFindings.length === 0}
            className="h-[40px] text-[16px] custom-button-disable"
            type="primary"
          >
            {t("Add")}
          </Button>,
        ]}
        width="80vw"
      >
        <div className="max-h-[75vh] overflow-y-auto scrollbar my-3 px-4 flex flex-col box-border">
          {isLoadingCard ? (
            <LoadingCards />
          ) : (
            <>
            {editableFindings.length > 0 ? 
          <Suggestions
              {...{
                editableFindings,
                setEditableFindings,
                isAddingMeasure,
                suggestionResponse,
                setSuggestionResponse,
                textSuggestionLoader,
                getTextSuggestions,
                apiLodaer,
                setApiLodaer,
                fieldImage,
                setFieldImage,
                imageUrl,
                setImageUrl,
              }}
            /> :
            <p>{t("No fields available")}</p>  
          }
            </>
          )}
        </div>
      </Modal>
      <Media
        mediaType={mediaType}
        source={mediaSource}
        showMedia={showMedia}
        setShowMedia={setShowMedia}
      />
    </>
  );
};

export default AddMeasureModal;
